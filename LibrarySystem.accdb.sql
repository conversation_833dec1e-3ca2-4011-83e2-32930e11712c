-- نظام إدارة المكتبة الاحترافي - قاعدة البيانات
-- Microsoft Access Database Structure

-- جدول المستخدمين
CREATE TABLE Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username VARCHAR(50) NOT NULL UNIQUE,
    Password VARCHAR(255) NOT NULL,
    FullName VARCHAR(100) NOT NULL,
    Email VARCHAR(100) UNIQUE,
    Phone VARCHAR(20),
    UserType VARCHAR(20) DEFAULT 'User', -- <PERSON><PERSON>, Librarian, User
    Status VARCHAR(20) DEFAULT 'Active', -- Active, Inactive, Suspended
    CreatedDate DATETIME DEFAULT NOW(),
    LastLogin DATETIME,
    ProfileImage VARCHAR(255),
    Department VARCHAR(50),
    Notes MEMO
);

-- جدول الكتب
CREATE TABLE Books (
    BookID AUTOINCREMENT PRIMARY KEY,
    ISBN VARCHAR(20) UNIQUE,
    Title VARCHAR(255) NOT NULL,
    Author VARCHAR(255) NOT NULL,
    Publisher VARCHAR(100),
    PublishYear INTEGER,
    Category VARCHAR(50),
    Language VARCHAR(30) DEFAULT 'Arabic',
    Pages INTEGER,
    Edition VARCHAR(20),
    Price CURRENCY,
    Quantity INTEGER DEFAULT 1,
    AvailableQuantity INTEGER DEFAULT 1,
    Location VARCHAR(50), -- موقع الكتاب في المكتبة
    Status VARCHAR(20) DEFAULT 'Available', -- Available, Borrowed, Lost, Damaged
    Description MEMO,
    CoverImage VARCHAR(255),
    AddedDate DATETIME DEFAULT NOW(),
    AddedBy INTEGER, -- Foreign Key to Users
    LastModified DATETIME,
    ModifiedBy INTEGER, -- Foreign Key to Users
    Barcode VARCHAR(50),
    Keywords VARCHAR(255)
);

-- جدول الفئات
CREATE TABLE Categories (
    CategoryID AUTOINCREMENT PRIMARY KEY,
    CategoryName VARCHAR(50) NOT NULL UNIQUE,
    Description MEMO,
    ParentCategoryID INTEGER, -- للفئات الفرعية
    Color VARCHAR(7) DEFAULT '#007bff', -- لون الفئة
    Icon VARCHAR(50), -- أيقونة الفئة
    CreatedDate DATETIME DEFAULT NOW(),
    IsActive YESNO DEFAULT TRUE
);

-- جدول الاستعارة
CREATE TABLE Borrowing (
    BorrowID AUTOINCREMENT PRIMARY KEY,
    BookID INTEGER NOT NULL, -- Foreign Key to Books
    UserID INTEGER NOT NULL, -- Foreign Key to Users
    BorrowDate DATETIME DEFAULT NOW(),
    DueDate DATETIME NOT NULL,
    ReturnDate DATETIME,
    Status VARCHAR(20) DEFAULT 'Borrowed', -- Borrowed, Returned, Overdue, Lost
    Fine CURRENCY DEFAULT 0,
    Notes MEMO,
    IssuedBy INTEGER, -- Foreign Key to Users (Librarian)
    ReturnedTo INTEGER, -- Foreign Key to Users (Librarian)
    RenewalCount INTEGER DEFAULT 0,
    LastRenewalDate DATETIME
);

-- جدول الحجوزات
CREATE TABLE Reservations (
    ReservationID AUTOINCREMENT PRIMARY KEY,
    BookID INTEGER NOT NULL, -- Foreign Key to Books
    UserID INTEGER NOT NULL, -- Foreign Key to Users
    ReservationDate DATETIME DEFAULT NOW(),
    ExpiryDate DATETIME,
    Status VARCHAR(20) DEFAULT 'Active', -- Active, Fulfilled, Cancelled, Expired
    Priority INTEGER DEFAULT 1,
    Notes MEMO,
    NotificationSent YESNO DEFAULT FALSE
);

-- جدول الغرامات
CREATE TABLE Fines (
    FineID AUTOINCREMENT PRIMARY KEY,
    UserID INTEGER NOT NULL, -- Foreign Key to Users
    BorrowID INTEGER, -- Foreign Key to Borrowing
    FineType VARCHAR(30), -- Late Return, Lost Book, Damage
    Amount CURRENCY NOT NULL,
    Description MEMO,
    FineDate DATETIME DEFAULT NOW(),
    DueDate DATETIME,
    PaidDate DATETIME,
    Status VARCHAR(20) DEFAULT 'Unpaid', -- Unpaid, Paid, Waived
    PaidAmount CURRENCY DEFAULT 0,
    WaivedBy INTEGER, -- Foreign Key to Users
    PaymentMethod VARCHAR(30)
);

-- جدول الأنشطة والسجلات
CREATE TABLE ActivityLog (
    LogID AUTOINCREMENT PRIMARY KEY,
    UserID INTEGER, -- Foreign Key to Users
    Action VARCHAR(100) NOT NULL,
    TableName VARCHAR(50),
    RecordID INTEGER,
    OldValues MEMO,
    NewValues MEMO,
    IPAddress VARCHAR(45),
    UserAgent VARCHAR(255),
    Timestamp DATETIME DEFAULT NOW(),
    SessionID VARCHAR(100),
    Result VARCHAR(20) DEFAULT 'Success' -- Success, Failed, Warning
);

-- جدول الإعدادات
CREATE TABLE Settings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingKey VARCHAR(50) NOT NULL UNIQUE,
    SettingValue MEMO,
    Description MEMO,
    Category VARCHAR(30),
    DataType VARCHAR(20) DEFAULT 'String', -- String, Integer, Boolean, Date
    IsEditable YESNO DEFAULT TRUE,
    LastModified DATETIME DEFAULT NOW(),
    ModifiedBy INTEGER -- Foreign Key to Users
);

-- جدول الإشعارات
CREATE TABLE Notifications (
    NotificationID AUTOINCREMENT PRIMARY KEY,
    UserID INTEGER, -- Foreign Key to Users (NULL for system-wide)
    Title VARCHAR(100) NOT NULL,
    Message MEMO NOT NULL,
    Type VARCHAR(30) DEFAULT 'Info', -- Info, Warning, Error, Success
    Priority VARCHAR(20) DEFAULT 'Normal', -- Low, Normal, High, Critical
    IsRead YESNO DEFAULT FALSE,
    CreatedDate DATETIME DEFAULT NOW(),
    ReadDate DATETIME,
    ExpiryDate DATETIME,
    ActionURL VARCHAR(255),
    Icon VARCHAR(50),
    Color VARCHAR(7)
);

-- جدول النسخ الاحتياطية
CREATE TABLE Backups (
    BackupID AUTOINCREMENT PRIMARY KEY,
    BackupName VARCHAR(100) NOT NULL,
    BackupPath VARCHAR(255) NOT NULL,
    BackupSize LONG,
    BackupType VARCHAR(30) DEFAULT 'Full', -- Full, Incremental, Differential
    Status VARCHAR(20) DEFAULT 'Completed', -- In Progress, Completed, Failed
    StartTime DATETIME,
    EndTime DATETIME,
    CreatedBy INTEGER, -- Foreign Key to Users
    Description MEMO,
    IsAutomatic YESNO DEFAULT FALSE,
    RetentionDays INTEGER DEFAULT 30
);

-- جدول المرفقات
CREATE TABLE Attachments (
    AttachmentID AUTOINCREMENT PRIMARY KEY,
    FileName VARCHAR(255) NOT NULL,
    OriginalName VARCHAR(255) NOT NULL,
    FilePath VARCHAR(500) NOT NULL,
    FileSize LONG,
    FileType VARCHAR(50),
    MimeType VARCHAR(100),
    RelatedTable VARCHAR(50), -- Books, Users, etc.
    RelatedID INTEGER,
    UploadedBy INTEGER, -- Foreign Key to Users
    UploadDate DATETIME DEFAULT NOW(),
    Description MEMO,
    IsPublic YESNO DEFAULT FALSE,
    DownloadCount INTEGER DEFAULT 0
);

-- إدراج البيانات الأساسية

-- إعدادات النظام الافتراضية
INSERT INTO Settings (SettingKey, SettingValue, Description, Category) VALUES
('SystemName', 'نظام إدارة المكتبة الاحترافي', 'اسم النظام', 'General'),
('MaxBorrowDays', '14', 'عدد أيام الاستعارة الافتراضية', 'Borrowing'),
('MaxBooksPerUser', '5', 'الحد الأقصى للكتب المستعارة لكل مستخدم', 'Borrowing'),
('FinePerDay', '1.00', 'الغرامة اليومية للتأخير', 'Fines'),
('SystemLanguage', 'Arabic', 'لغة النظام الافتراضية', 'General'),
('EmailNotifications', 'True', 'تفعيل الإشعارات عبر البريد الإلكتروني', 'Notifications'),
('AutoBackup', 'True', 'تفعيل النسخ الاحتياطي التلقائي', 'Backup'),
('BackupInterval', '24', 'فترة النسخ الاحتياطي بالساعات', 'Backup');

-- الفئات الافتراضية
INSERT INTO Categories (CategoryName, Description, Color, Icon) VALUES
('الأدب العربي', 'كتب الأدب والشعر العربي', '#e74c3c', 'book'),
('العلوم', 'كتب العلوم والرياضيات', '#3498db', 'flask'),
('التاريخ', 'كتب التاريخ والحضارة', '#f39c12', 'clock'),
('الدين', 'الكتب الدينية والإسلامية', '#27ae60', 'mosque'),
('التكنولوجيا', 'كتب البرمجة والتكنولوجيا', '#9b59b6', 'laptop'),
('الطب', 'الكتب الطبية والصحية', '#e67e22', 'heartbeat'),
('القانون', 'كتب القانون والتشريع', '#34495e', 'gavel'),
('الاقتصاد', 'كتب الاقتصاد والمالية', '#16a085', 'chart-line');

-- مستخدم المدير الافتراضي
INSERT INTO Users (Username, Password, FullName, Email, UserType, Status) VALUES
('admin', 'admin123', 'مدير النظام', '<EMAIL>', 'Admin', 'Active');
