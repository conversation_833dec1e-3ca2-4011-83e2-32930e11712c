<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المكتبة الاحترافي</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* اسم المبرمج المتحرك */
        .programmer-name {
            position: fixed;
            top: 10px;
            left: 20px;
            z-index: 1001;
            font-size: 18px;
            font-weight: 700;
            background: linear-gradient(45deg, #3498db, #f39c12, #9b59b6, #e91e63);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite, bounce 2s ease-in-out infinite;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .programmer-name:hover {
            transform: scale(1.1) rotate(2deg);
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* الشريط العلوي */
        .top-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .top-bar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
        }

        .logo i {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 12px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notifications {
            position: relative;
            cursor: pointer;
        }

        .notification-icon {
            font-size: 20px;
            color: #7f8c8d;
            transition: all 0.3s ease;
        }

        .notification-icon:hover {
            color: #3498db;
            transform: scale(1.1);
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            padding: 8px 15px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .user-profile:hover {
            background: rgba(52, 152, 219, 0.1);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* الحاوي الرئيسي */
        .main-container {
            display: flex;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }

        /* الشريط الجانبي */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: fixed;
            height: calc(100vh - 80px);
            overflow-y: auto;
            z-index: 999;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-toggle {
            position: absolute;
            top: 20px;
            left: -15px;
            background: #3498db;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: #2980b9;
            transform: scale(1.1);
        }

        .sidebar-menu {
            padding: 60px 0 20px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .menu-item:hover {
            background: linear-gradient(90deg, rgba(52, 152, 219, 0.1), transparent);
            border-right-color: #3498db;
            transform: translateX(-5px);
        }

        .menu-item.active {
            background: linear-gradient(90deg, rgba(52, 152, 219, 0.15), transparent);
            border-right-color: #3498db;
            color: #3498db;
            font-weight: 600;
        }

        .menu-item i {
            width: 20px;
            margin-left: 15px;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .menu-item:hover i {
            transform: scale(1.2);
        }

        .menu-text {
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .menu-text {
            opacity: 0;
            transform: translateX(20px);
        }

        /* المحتوى الرئيسي */
        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 30px;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed + .main-content {
            margin-right: 70px;
        }

        /* بطاقات الأقسام */
        .section-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .section-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .section-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2980b9);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .section-card:hover::before {
            transform: scaleX(1);
        }

        .section-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .card-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        /* الأدوات العائمة */
        .floating-tools {
            position: fixed;
            bottom: 30px;
            left: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 1000;
        }

        .floating-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-btn:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .btn-primary { background: linear-gradient(45deg, #3498db, #2980b9); }
        .btn-success { background: linear-gradient(45deg, #27ae60, #229954); }
        .btn-warning { background: linear-gradient(45deg, #f39c12, #e67e22); }

        /* الرسوم المتحركة */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .section-card:nth-child(1) { animation-delay: 0.1s; }
        .section-card:nth-child(2) { animation-delay: 0.2s; }
        .section-card:nth-child(3) { animation-delay: 0.3s; }
        .section-card:nth-child(4) { animation-delay: 0.4s; }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
                padding: 20px;
            }

            .top-bar-content {
                padding: 0 15px;
            }

            .floating-tools {
                bottom: 20px;
                left: 20px;
            }

            .section-cards {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .logo span {
                display: none;
            }

            .user-info {
                gap: 10px;
            }

            .user-profile div {
                display: none;
            }
        }

        /* تحسينات إضافية */
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tooltip {
            position: relative;
            cursor: pointer;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
            visibility: visible;
        }

        /* تأثيرات متقدمة */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .glow-effect {
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.3);
            transition: box-shadow 0.3s ease;
        }

        .glow-effect:hover {
            box-shadow: 0 0 30px rgba(52, 152, 219, 0.5);
        }

        /* أنماط إضافية للألوان */
        .color-blue { background: linear-gradient(45deg, #3498db, #2980b9); }
        .color-green { background: linear-gradient(45deg, #27ae60, #229954); }
        .color-red { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .color-orange { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .color-purple { background: linear-gradient(45deg, #9b59b6, #8e44ad); }
        .color-teal { background: linear-gradient(45deg, #1abc9c, #16a085); }
        .color-indigo { background: linear-gradient(45deg, #6c5ce7, #5f3dc4); }
        .color-pink { background: linear-gradient(45deg, #fd79a8, #e84393); }
        .color-gray { background: linear-gradient(45deg, #95a5a6, #7f8c8d); }
    </style>
</head>
<body>


    <!-- الشريط العلوي -->
    <div class="top-bar">
        <div class="top-bar-content">
            <div style="display: flex; align-items: center; gap: 15px;">
                <button id="mobileMenuToggle" onclick="toggleMobileSidebar()" style="display: none; background: none; border: none; font-size: 20px; color: #2c3e50; cursor: pointer; padding: 8px;">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <i class="fas fa-book-open"></i>
                    <span>نظام إدارة المكتبة الاحترافي</span>
                </div>
            </div>
            <div class="user-info">
                <div class="notifications tooltip" data-tooltip="الإشعارات" onclick="showNotifications()">
                    <i class="fas fa-bell notification-icon"></i>
                    <span class="notification-badge">3</span>
                </div>

            </div>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="main-container">
        <!-- الشريط الجانبي -->
        <div class="sidebar" id="sidebar">
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            <nav class="sidebar-menu">
                <a href="#" class="menu-item active" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="menu-text">لوحة المعلومات</span>
                </a>
                <a href="#" class="menu-item" onclick="showSection('books')">
                    <i class="fas fa-book"></i>
                    <span class="menu-text">إدارة الكتب</span>
                </a>

                <a href="#" class="menu-item" onclick="showSection('search')">
                    <i class="fas fa-search"></i>
                    <span class="menu-text">البحث المتقدم</span>
                </a>

                <a href="#" class="menu-item" onclick="showSection('archive')">
                    <i class="fas fa-archive"></i>
                    <span class="menu-text">الأرشيف</span>
                </a>

                <a href="#" class="menu-item" onclick="showSection('backup')">
                    <i class="fas fa-database"></i>
                    <span class="menu-text">النسخ الاحتياطي</span>
                </a>
                <a href="#" class="menu-item" onclick="showSection('settings')">
                    <i class="fas fa-cog"></i>
                    <span class="menu-text">الإعدادات</span>
                </a>
            </nav>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content" id="mainContent">
            <!-- سيتم تحميل المحتوى هنا ديناميكياً -->
        </div>
    </div>

    <!-- الأدوات العائمة -->
    <div class="floating-tools">
        <button class="floating-btn btn-primary" onclick="quickAddBook()" title="إضافة كتاب سريع">
            <i class="fas fa-plus"></i>
        </button>
        <button class="floating-btn btn-success" onclick="quickScan()" title="مسح سريع">
            <i class="fas fa-qrcode"></i>
        </button>
        <button class="floating-btn btn-warning" onclick="quickSearch()" title="بحث سريع">
            <i class="fas fa-search"></i>
        </button>
    </div>

    <script>
        // متغيرات عامة
        let currentSection = 'dashboard';
        let sidebarCollapsed = false;
        let progressBar = null;
        let currentModal = null;

        // تحميل لوحة المعلومات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            showSection('dashboard');
            initializeSystem();
        });

        // 1. وظيفة تهيئة النظام
        function initializeSystem() {
            console.log('تم تهيئة النظام بنجاح');
            showAlert('مرحباً بك في نظام إدارة المكتبة الاحترافي', 'success');
        }

        // 2. وظيفة تبديل الشريط الجانبي
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('.main-content');

            sidebarCollapsed = !sidebarCollapsed;

            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
            } else {
                sidebar.classList.remove('collapsed');
            }
        }

        // 3. وظيفة عرض الأقسام
        function showSection(sectionName) {
            currentSection = sectionName;

            // تحديث القائمة النشطة
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            event.target.closest('.menu-item').classList.add('active');

            // عرض المحتوى المناسب
            const mainContent = document.getElementById('mainContent');

            switch(sectionName) {
                case 'dashboard':
                    mainContent.innerHTML = getDashboardContent();
                    break;
                case 'books':
                    mainContent.innerHTML = getBooksContent();
                    break;
                case 'scan':
                    mainContent.innerHTML = getScanContent();
                    break;
                case 'search':
                    mainContent.innerHTML = getSearchContent();
                    break;
                case 'archive':
                    mainContent.innerHTML = getArchiveContent();
                    break;
                case 'backup':
                    mainContent.innerHTML = getBackupContent();
                    break;
                case 'settings':
                    mainContent.innerHTML = getSettingsContent();
                    break;
                default:
                    mainContent.innerHTML = getDashboardContent();
            }
        }

        // 4. محتوى لوحة المعلومات
        function getDashboardContent() {
            return `
                <div style="margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; font-size: 32px; font-weight: 700; margin-bottom: 10px;">
                        <i class="fas fa-tachometer-alt" style="color: #3498db; margin-left: 15px;"></i>
                        لوحة المعلومات
                    </h1>
                    <p style="color: #7f8c8d; font-size: 16px;">نظرة شاملة على إحصائيات المكتبة والأنشطة الحديثة</p>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="section-cards">
                    <div class="section-card">
                        <div class="card-header">
                            <div class="card-icon color-blue">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="card-title">إجمالي الكتب</div>
                        </div>
                        <div class="card-description">العدد الكلي للكتب المتاحة في المكتبة</div>
                        <div class="card-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="totalBooks">0</div>
                                <div class="stat-label">كتاب</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="availableBooks">0</div>
                                <div class="stat-label">متاح</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="borrowedBooks">0</div>
                                <div class="stat-label">مستعار</div>
                            </div>
                        </div>
                    </div>

                    <div class="section-card">
                        <div class="card-header">
                            <div class="card-icon color-green">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-title">المستخدمين النشطين</div>
                        </div>
                        <div class="card-description">عدد المستخدمين المسجلين والنشطين</div>
                        <div class="card-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="totalUsers">0</div>
                                <div class="stat-label">مسجل</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="activeUsers">0</div>
                                <div class="stat-label">نشط</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="newUsers">0</div>
                                <div class="stat-label">جديد</div>
                            </div>
                        </div>
                    </div>

                    <div class="section-card">
                        <div class="card-header">
                            <div class="card-icon color-orange">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="card-title">عمليات الاستعارة</div>
                        </div>
                        <div class="card-description">إحصائيات الاستعارة والإرجاع اليومية</div>
                        <div class="card-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="todayTransactions">0</div>
                                <div class="stat-label">اليوم</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="weekTransactions">0</div>
                                <div class="stat-label">هذا الأسبوع</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="monthTransactions">0</div>
                                <div class="stat-label">هذا الشهر</div>
                            </div>
                        </div>
                    </div>

                    <div class="section-card">
                        <div class="card-header">
                            <div class="card-icon color-red">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-title">التنبيهات</div>
                        </div>
                        <div class="card-description">الكتب المتأخرة والتنبيهات المهمة</div>
                        <div class="card-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="overdueBooks">0</div>
                                <div class="stat-label">متأخر</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="lostBooks">0</div>
                                <div class="stat-label">مفقود</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="damagedBooks">0</div>
                                <div class="stat-label">تالف</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الأنشطة الحديثة -->
                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-top: 30px;">
                    <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600; margin-bottom: 20px;">
                        <i class="fas fa-clock" style="color: #3498db; margin-left: 10px;"></i>
                        الأنشطة الحديثة
                    </h2>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: rgba(52, 152, 219, 0.1);">
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الوقت</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">النشاط</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">المستخدم</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">التفاصيل</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid rgba(0,0,0,0.1);">
                                    <td style="padding: 15px; color: #7f8c8d;">تم مسح البيانات</td>
                                    <td style="padding: 15px; color: #2c3e50;">-</td>
                                    <td style="padding: 15px; color: #2c3e50;">-</td>
                                    <td style="padding: 15px; color: #7f8c8d;">-</td>
                                    <td style="padding: 15px;"><span style="background: #95a5a6; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">فارغ</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // 5. وظيفة عرض التنبيهات
        function showAlert(message, type = 'info', duration = 3000) {
            const alertContainer = document.createElement('div');
            alertContainer.style.cssText = `
                position: fixed;
                top: 100px;
                right: 30px;
                z-index: 10000;
                padding: 15px 20px;
                border-radius: 10px;
                color: white;
                font-weight: 600;
                box-shadow: 0 5px 20px rgba(0,0,0,0.3);
                transform: translateX(100%);
                transition: all 0.3s ease;
                max-width: 400px;
            `;

            // تحديد لون التنبيه
            switch(type) {
                case 'success':
                    alertContainer.style.background = 'linear-gradient(45deg, #27ae60, #229954)';
                    break;
                case 'error':
                    alertContainer.style.background = 'linear-gradient(45deg, #e74c3c, #c0392b)';
                    break;
                case 'warning':
                    alertContainer.style.background = 'linear-gradient(45deg, #f39c12, #e67e22)';
                    break;
                default:
                    alertContainer.style.background = 'linear-gradient(45deg, #3498db, #2980b9)';
            }

            alertContainer.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 16px; cursor: pointer; margin-right: auto;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(alertContainer);

            // إظهار التنبيه
            setTimeout(() => {
                alertContainer.style.transform = 'translateX(0)';
            }, 100);

            // إخفاء التنبيه تلقائياً
            setTimeout(() => {
                alertContainer.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (alertContainer.parentElement) {
                        alertContainer.remove();
                    }
                }, 300);
            }, duration);
        }

        // 6. محتوى إدارة الكتب
        function getBooksContent() {
            return `
                <div style="margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; font-size: 32px; font-weight: 700; margin-bottom: 10px;">
                        <i class="fas fa-book" style="color: #27ae60; margin-left: 15px;"></i>
                        إدارة الكتب
                    </h1>
                    <p style="color: #7f8c8d; font-size: 16px;">إضافة وتعديل وحذف الكتب مع إمكانيات الاستيراد والتصدير</p>
                </div>

                <!-- نموذج إضافة كتاب جديد -->
                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
                    <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600; margin-bottom: 20px;">
                        <i class="fas fa-plus-circle" style="color: #27ae60; margin-left: 10px;"></i>
                        إضافة كتاب جديد
                    </h2>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">رقم الكتاب</label>
                            <input type="text" id="bookNumber" placeholder="رقم الكتاب..." style="width: 100%; padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                        </div>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px;">
                            <div>
                                <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">اليوم</label>
                                <input type="number" id="bookDay" min="1" max="31" placeholder="يوم" style="width: 100%; padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                            </div>
                            <div>
                                <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الشهر</label>
                                <input type="number" id="bookMonth" min="1" max="12" placeholder="شهر" style="width: 100%; padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                            </div>
                            <div>
                                <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">السنة</label>
                                <input type="number" id="bookYear" min="1900" max="2030" placeholder="سنة" style="width: 100%; padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الموضوع</label>
                            <textarea id="bookSubject" rows="4" placeholder="موضوع الكتاب..." style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; resize: vertical;"></textarea>
                        </div>
                        
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">التفاصيل</label>
                            <textarea id="bookDetails" rows="4" placeholder="تفاصيل الكتاب..." style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; resize: vertical;"></textarea>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 300px; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">التنفيذ</label>
                            <textarea id="bookExecution" rows="3" placeholder="تفاصيل التنفيذ..." style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; resize: vertical;"></textarea>
                        </div>
                        
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">صورة الكتاب</label>
                            <div style="border: 3px dashed #bdc3c7; border-radius: 15px; padding: 20px; text-align: center; background: #f8f9fa; min-height: 120px; display: flex; flex-direction: column; justify-content: center;">
                                <i class="fas fa-camera" style="font-size: 32px; color: #7f8c8d; margin-bottom: 10px;"></i>
                                <button onclick="startCanonScanner()" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; margin-bottom: 10px;">
                                    <i class="fas fa-scanner" style="margin-left: 8px;"></i>
                                    تشغيل الماسح الضوئي DR-C240
                                </button>
                                <small style="color: #7f8c8d;">أو اسحب الصور هنا</small>
                                <input type="file" id="bookImages" multiple accept="image/*" style="display: none;" onchange="handleBookImages(this.files)">
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center;">
                        <button onclick="saveBookData()" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 15px 40px; border-radius: 10px; cursor: pointer; font-weight: 600; font-size: 16px; margin: 0 10px;">
                            <i class="fas fa-save" style="margin-left: 8px;"></i>
                            حفظ بيانات الكتاب
                        </button>
                        <button onclick="clearBookForm()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 15px 40px; border-radius: 10px; cursor: pointer; font-weight: 600; font-size: 16px; margin: 0 10px;">
                            <i class="fas fa-eraser" style="margin-left: 8px;"></i>
                            مسح الحقول
                        </button>
                    </div>
                </div>

                <div style="display: flex; gap: 20px; margin-bottom: 30px; flex-wrap: wrap;">
                    <button onclick="addNewBook()" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-plus" style="margin-left: 8px;"></i>
                        إضافة كتاب جديد
                    </button>
                    <button onclick="importBooks()" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-upload" style="margin-left: 8px;"></i>
                        استيراد كتب
                    </button>
                    <button onclick="exportBooks()" style="background: linear-gradient(45deg, #f39c12, #e67e22); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-download" style="margin-left: 8px;"></i>
                        تصدير البيانات
                    </button>
                </div>

                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600;">قائمة الكتب</h2>
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <input type="text" placeholder="البحث في الكتب..." style="padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 25px; width: 250px; outline: none;">
                            <select style="padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                                <option>جميع الفئات</option>
                                <option>الأدب العربي</option>
                                <option>العلوم</option>
                                <option>التاريخ</option>
                                <option>التكنولوجيا</option>
                            </select>
                        </div>
                    </div>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: rgba(52, 152, 219, 0.1);">
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">العنوان</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">المؤلف</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الفئة</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الحالة</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid rgba(0,0,0,0.1);">
                                    <td style="padding: 15px; color: #7f8c8d; text-align: center;" colspan="5">
                                        <i class="fas fa-inbox" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px; display: block;"></i>
                                        <p style="color: #95a5a6; font-size: 16px;">لا توجد بيانات محفوظة حالياً</p>
                                        <p style="color: #7f8c8d; font-size: 14px;">قم بإضافة كتاب جديد لبدء استخدام النظام</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // 7. محتوى المسح الضوئي
        function getScanContent() {
            return `
                <div style="margin-bottom: 30px;">
                    <div class="programmer-name" style="position: relative; top: 0; left: 0; margin-bottom: 15px;">
                        علي عاجل خشام المحنة
                    </div>
                    <h1 style="color: #2c3e50; font-size: 32px; font-weight: 700; margin-bottom: 10px;">
                        <i class="fas fa-camera" style="color: #9b59b6; margin-left: 15px;"></i>
                        المسح الضوئي
                    </h1>
                    <p style="color: #7f8c8d; font-size: 16px;">مسح الباركود والكتب مع إمكانية المسح المتعدد وتحويل PDF</p>
                </div>

                <div class="section-cards">
                    <div class="section-card" onclick="startBarcodeScanning()">
                        <div class="card-header">
                            <div class="card-icon color-purple">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="card-title">مسح الباركود</div>
                        </div>
                        <div class="card-description">مسح باركود الكتب للبحث السريع والاستعارة</div>
                    </div>

                    <div class="section-card" onclick="startImageScanning()">
                        <div class="card-header">
                            <div class="card-icon color-teal">
                                <i class="fas fa-image"></i>
                            </div>
                            <div class="card-title">مسح الصور</div>
                        </div>
                        <div class="card-description">رفع وتحليل صور الكتب والمستندات</div>
                    </div>

                    <div class="section-card" onclick="startMultiScanning()">
                        <div class="card-header">
                            <div class="card-icon color-indigo">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="card-title">المسح المتعدد</div>
                        </div>
                        <div class="card-description">مسح عدة كتب في عملية واحدة</div>
                    </div>

                    <div class="section-card" onclick="convertToPDF()">
                        <div class="card-header">
                            <div class="card-icon color-red">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <div class="card-title">تحويل PDF</div>
                        </div>
                        <div class="card-description">تحويل الصور الممسوحة إلى ملفات PDF</div>
                    </div>
                </div>

                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-top: 30px;">
                    <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600; margin-bottom: 20px;">
                        <i class="fas fa-upload" style="color: #3498db; margin-left: 10px;"></i>
                        منطقة الرفع
                    </h2>
                    <div id="dropZone" style="border: 3px dashed #bdc3c7; border-radius: 15px; padding: 50px; text-align: center; cursor: pointer; transition: all 0.3s ease;"
                         ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #bdc3c7; margin-bottom: 20px;"></i>
                        <h3 style="color: #7f8c8d; margin-bottom: 10px;">اسحب الملفات هنا أو انقر للاختيار</h3>
                        <p style="color: #95a5a6;">يدعم: JPG, PNG, PDF, TIFF</p>
                        <input type="file" id="fileInput" multiple accept="image/*,.pdf" style="display: none;" onchange="handleFileSelect(event)">
                    </div>

                    <div id="scanResults" style="margin-top: 30px; display: none;">
                        <h3 style="color: #2c3e50; margin-bottom: 15px;">نتائج المسح</h3>
                        <div id="resultsList"></div>
                    </div>
                </div>
            `;
        }

        // 8. محتوى البحث المتقدم
        function getSearchContent() {
            return `
                <div style="margin-bottom: 30px;">
                    <div class="programmer-name" style="position: relative; top: 0; left: 0; margin-bottom: 15px;">
                        علي عاجل خشام المحنة
                    </div>
                    <h1 style="color: #2c3e50; font-size: 32px; font-weight: 700; margin-bottom: 10px;">
                        <i class="fas fa-search" style="color: #1abc9c; margin-left: 15px;"></i>
                        البحث المتقدم
                    </h1>
                    <p style="color: #7f8c8d; font-size: 16px;">بحث شامل ومتقدم في قاعدة بيانات المكتبة</p>
                </div>

                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
                    <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600; margin-bottom: 20px;">معايير البحث</h2>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">بحث حسب العنوان</label>
                            <input type="text" id="searchByTitle" placeholder="ادخل عنوان الكتاب..." style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>

                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">بحث حسب التنفيذ</label>
                            <input type="text" id="searchByExecution" placeholder="ادخل اسم التنفيذ..." style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>

                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">بحث من تاريخ</label>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px;">
                                <input type="number" id="fromDay" placeholder="يوم" min="1" max="31" style="padding: 10px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                                <input type="number" id="fromMonth" placeholder="شهر" min="1" max="12" style="padding: 10px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                                <input type="number" id="fromYear" placeholder="سنة" min="1900" max="2030" style="padding: 10px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                            </div>
                        </div>

                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">بحث إلى تاريخ</label>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px;">
                                <input type="number" id="toDay" placeholder="يوم" min="1" max="31" style="padding: 10px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                                <input type="number" id="toMonth" placeholder="شهر" min="1" max="12" style="padding: 10px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                                <input type="number" id="toYear" placeholder="سنة" min="1900" max="2030" style="padding: 10px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                            </div>
                        </div>

                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">بحث حسب أي كلمة من التفاصيل</label>
                            <input type="text" id="searchByDetails" placeholder="ادخل كلمة من التفاصيل..." style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; margin-top: 25px; justify-content: center;">
                        <button onclick="performAdvancedSearch()" style="background: linear-gradient(45deg, #1abc9c, #16a085); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-search" style="margin-left: 8px;"></i>
                            بحث متقدم
                        </button>
                        <button onclick="clearAdvancedSearchForm()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-eraser" style="margin-left: 8px;"></i>
                            مسح الحقول
                        </button>
                    </div>
                </div>

                <div id="searchResults" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); display: none;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600;">نتائج البحث</h2>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <span id="resultsCount" style="background: #3498db; color: white; padding: 5px 15px; border-radius: 20px; font-size: 14px; font-weight: 600;">0 نتيجة</span>
                            <button onclick="exportSearchResults()" style="background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 8px; cursor: pointer;">
                                <i class="fas fa-download" style="margin-left: 5px;"></i>
                                تصدير
                            </button>
                        </div>
                    </div>
                    <div style="overflow-x: auto; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        <table id="searchResultsTable" style="width: 100%; border-collapse: collapse; background: white; font-family: 'Cairo', sans-serif;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <th style="padding: 15px; text-align: right; font-weight: 600; border-bottom: 2px solid #5a67d8;">رقم الكتاب</th>
                                    <th style="padding: 15px; text-align: right; font-weight: 600; border-bottom: 2px solid #5a67d8;">التاريخ</th>
                                    <th style="padding: 15px; text-align: right; font-weight: 600; border-bottom: 2px solid #5a67d8;">الموضوع</th>
                                    <th style="padding: 15px; text-align: right; font-weight: 600; border-bottom: 2px solid #5a67d8;">التفاصيل</th>
                                    <th style="padding: 15px; text-align: right; font-weight: 600; border-bottom: 2px solid #5a67d8;">التنفيذ</th>
                                    <th style="padding: 15px; text-align: right; font-weight: 600; border-bottom: 2px solid #5a67d8;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="searchResultsBody">
                                <tr>
                                    <td colspan="6" style="padding: 40px; text-align: center; color: #7f8c8d;">
                                        <i class="fas fa-search" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px; display: block;"></i>
                                        <p style="font-size: 16px; margin-bottom: 10px;">قم بإجراء بحث لعرض النتائج</p>
                                        <p style="font-size: 14px; color: #95a5a6;">استخدم الحقول أعلاه للبحث في قاعدة البيانات</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // 9. محتوى التقارير
        function getReportsContent() {
            return `
                <div style="margin-bottom: 30px;">
                    <div class="programmer-name" style="position: relative; top: 0; left: 0; margin-bottom: 15px;">
                        علي عاجل خشام المحنة
                    </div>
                    <h1 style="color: #2c3e50; font-size: 32px; font-weight: 700; margin-bottom: 10px;">
                        <i class="fas fa-chart-bar" style="color: #e67e22; margin-left: 15px;"></i>
                        التقارير والإحصائيات
                    </h1>
                    <p style="color: #7f8c8d; font-size: 16px;">تقارير شاملة ومفصلة عن أداء المكتبة</p>
                </div>

                <div class="section-cards">
                    <div class="section-card" onclick="generateBorrowingReport()">
                        <div class="card-header">
                            <div class="card-icon color-blue">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="card-title">تقرير الاستعارة</div>
                        </div>
                        <div class="card-description">إحصائيات مفصلة عن عمليات الاستعارة والإرجاع</div>
                    </div>

                    <div class="section-card" onclick="generateUsersReport()">
                        <div class="card-header">
                            <div class="card-icon color-green">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-title">تقرير المستخدمين</div>
                        </div>
                        <div class="card-description">تحليل نشاط المستخدمين وسلوكيات القراءة</div>
                    </div>

                    <div class="section-card" onclick="generateInventoryReport()">
                        <div class="card-header">
                            <div class="card-icon color-orange">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div class="card-title">تقرير المخزون</div>
                        </div>
                        <div class="card-description">حالة المخزون والكتب المتاحة والمفقودة</div>
                    </div>

                    <div class="section-card" onclick="generateFinancialReport()">
                        <div class="card-header">
                            <div class="card-icon color-purple">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="card-title">التقرير المالي</div>
                        </div>
                        <div class="card-description">الغرامات والرسوم والإيرادات المالية</div>
                    </div>
                </div>

                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-top: 30px;">
                    <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600; margin-bottom: 20px;">إعدادات التقرير</h2>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 25px;">
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">نوع التقرير</label>
                            <select id="reportType" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                                <option value="borrowing">تقرير الاستعارة</option>
                                <option value="users">تقرير المستخدمين</option>
                                <option value="inventory">تقرير المخزون</option>
                                <option value="financial">التقرير المالي</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الفترة الزمنية</label>
                            <select id="reportPeriod" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="quarter">هذا الربع</option>
                                <option value="year">هذا العام</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">تنسيق التصدير</label>
                            <select id="exportFormat" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                                <option value="pdf">PDF</option>
                                <option value="excel">Excel</option>
                                <option value="csv">CSV</option>
                                <option value="html">HTML</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="generateReport()" style="background: linear-gradient(45deg, #e67e22, #d35400); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
                            إنشاء التقرير
                        </button>
                        <button onclick="scheduleReport()" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-clock" style="margin-left: 8px;"></i>
                            جدولة التقرير
                        </button>
                        <button onclick="exportReport()" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-download" style="margin-left: 8px;"></i>
                            تصدير التقرير
                        </button>
                    </div>
                </div>
            `;
        }

        // 10. محتوى الأرشيف
        function getArchiveContent() {
            return `
                <div style="margin-bottom: 30px;">
                    <div class="programmer-name" style="position: relative; top: 0; left: 0; margin-bottom: 15px;">
                        علي عاجل خشام المحنة
                    </div>
                    <h1 style="color: #2c3e50; font-size: 32px; font-weight: 700; margin-bottom: 10px;">
                        <i class="fas fa-archive" style="color: #34495e; margin-left: 15px;"></i>
                        إدارة الأرشيف
                    </h1>
                    <p style="color: #7f8c8d; font-size: 16px;">أرشفة وإدارة الكتب والمستندات مع نظام صلاحيات متقدم</p>
                </div>

                <div style="display: flex; gap: 20px; margin-bottom: 30px; flex-wrap: wrap;">
                    <button onclick="createArchiveFolder()" style="background: linear-gradient(45deg, #34495e, #2c3e50); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-folder-plus" style="margin-left: 8px;"></i>
                        إنشاء مجلد أرشيف
                    </button>
                    <button onclick="archiveSelectedItems()" style="background: linear-gradient(45deg, #7f8c8d, #95a5a6); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-archive" style="margin-left: 8px;"></i>
                        أرشفة العناصر المحددة
                    </button>
                    <button onclick="restoreFromArchive()" style="background: linear-gradient(45deg, #16a085, #1abc9c); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-undo" style="margin-left: 8px;"></i>
                        استعادة من الأرشيف
                    </button>
                </div>

                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600;">محتويات الأرشيف</h2>
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <select style="padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                                <option>جميع المجلدات</option>
                                <option>الكتب المؤرشفة</option>
                                <option>المستندات القديمة</option>
                                <option>النسخ الاحتياطية</option>
                            </select>
                            <button onclick="toggleArchiveView()" style="background: #3498db; color: white; border: none; padding: 10px 15px; border-radius: 8px; cursor: pointer;">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px;">
                        <div style="background: rgba(52, 152, 219, 0.1); border-radius: 15px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.3s ease;" onclick="openArchiveFolder('books')">
                            <i class="fas fa-folder" style="font-size: 48px; color: #3498db; margin-bottom: 15px;"></i>
                            <h3 style="color: #2c3e50; margin-bottom: 5px;">الكتب المؤرشفة</h3>
                            <p style="color: #7f8c8d; font-size: 14px;">0 عنصر</p>
                        </div>

                        <div style="background: rgba(231, 76, 60, 0.1); border-radius: 15px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.3s ease;" onclick="openArchiveFolder('documents')">
                            <i class="fas fa-file-alt" style="font-size: 48px; color: #e74c3c; margin-bottom: 15px;"></i>
                            <h3 style="color: #2c3e50; margin-bottom: 5px;">المستندات</h3>
                            <p style="color: #7f8c8d; font-size: 14px;">0 عنصر</p>
                        </div>

                        <div style="background: rgba(39, 174, 96, 0.1); border-radius: 15px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.3s ease;" onclick="openArchiveFolder('backups')">
                            <i class="fas fa-database" style="font-size: 48px; color: #27ae60; margin-bottom: 15px;"></i>
                            <h3 style="color: #2c3e50; margin-bottom: 5px;">النسخ الاحتياطية</h3>
                            <p style="color: #7f8c8d; font-size: 14px;">0 عنصر</p>
                        </div>

                        <div style="background: rgba(155, 89, 182, 0.1); border-radius: 15px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.3s ease;" onclick="openArchiveFolder('media')">
                            <i class="fas fa-images" style="font-size: 48px; color: #9b59b6; margin-bottom: 15px;"></i>
                            <h3 style="color: #2c3e50; margin-bottom: 5px;">الوسائط</h3>
                            <p style="color: #7f8c8d; font-size: 14px;">0 عنصر</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // 11. محتوى المستخدمين
        function getUsersContent() {
            return `
                <div style="margin-bottom: 30px;">
                    <div class="programmer-name" style="position: relative; top: 0; left: 0; margin-bottom: 15px;">
                        علي عاجل خشام المحنة
                    </div>
                    <h1 style="color: #2c3e50; font-size: 32px; font-weight: 700; margin-bottom: 10px;">
                        <i class="fas fa-users" style="color: #2980b9; margin-left: 15px;"></i>
                        إدارة المستخدمين
                    </h1>
                    <p style="color: #7f8c8d; font-size: 16px;">إدارة شاملة للمستخدمين والصلاحيات والأدوار</p>
                </div>

                <div style="display: flex; gap: 20px; margin-bottom: 30px; flex-wrap: wrap;">
                    <button onclick="addNewUser()" style="background: linear-gradient(45deg, #2980b9, #3498db); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-user-plus" style="margin-left: 8px;"></i>
                        إضافة مستخدم جديد
                    </button>
                    <button onclick="importUsers()" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-upload" style="margin-left: 8px;"></i>
                        استيراد مستخدمين
                    </button>
                    <button onclick="exportUsers()" style="background: linear-gradient(45deg, #f39c12, #e67e22); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-download" style="margin-left: 8px;"></i>
                        تصدير قائمة المستخدمين
                    </button>
                </div>

                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600;">قائمة المستخدمين</h2>
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <input type="text" placeholder="البحث في المستخدمين..." style="padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 25px; width: 250px; outline: none;">
                            <select style="padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                                <option>جميع الأدوار</option>
                                <option>مدير</option>
                                <option>أمين مكتبة</option>
                                <option>مستخدم عادي</option>
                            </select>
                        </div>
                    </div>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: rgba(52, 152, 219, 0.1);">
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الاسم</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">البريد الإلكتروني</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الدور</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الحالة</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">آخر دخول</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid rgba(0,0,0,0.1);">
                                    <td style="padding: 15px; color: #7f8c8d; text-align: center;" colspan="6">
                                        <i class="fas fa-users" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px; display: block;"></i>
                                        <p style="color: #95a5a6; font-size: 16px;">لا يوجد مستخدمين مسجلين حالياً</p>
                                        <p style="color: #7f8c8d; font-size: 14px;">قم بإضافة مستخدمين جدد لبدء استخدام النظام</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // 12. محتوى النسخ الاحتياطي المطور
        function getBackupContent() {
            // تحديث قائمة النسخ الاحتياطية
            updateBackupList();

            return `
                <div style="margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; font-size: 32px; font-weight: 700; margin-bottom: 10px;">
                        <i class="fas fa-database" style="color: #16a085; margin-left: 15px;"></i>
                        النسخ الاحتياطي
                    </h1>
                    <p style="color: #7f8c8d; font-size: 16px;">إنشاء واستعادة النسخ الاحتياطية للنظام مع حفظ جميع البيانات والصور</p>
                </div>

                <div class="section-cards">
                    <div class="section-card" onclick="createRealBackup()">
                        <div class="card-header">
                            <div class="card-icon color-teal">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="card-title">إنشاء نسخة احتياطية</div>
                        </div>
                        <div class="card-description">إنشاء نسخة احتياطية فورية من جميع البيانات والصور مع اختيار مكان الحفظ</div>
                        <div class="card-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="totalDataSize">0</div>
                                <div class="stat-label">MB البيانات</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="totalImages">0</div>
                                <div class="stat-label">صورة</div>
                            </div>
                        </div>
                    </div>

                    <div class="section-card" onclick="restoreRealBackup()">
                        <div class="card-header">
                            <div class="card-icon color-blue">
                                <i class="fas fa-undo"></i>
                            </div>
                            <div class="card-title">استعادة النسخة</div>
                        </div>
                        <div class="card-description">استعادة النظام من نسخة احتياطية محددة مع اختيار ملف النسخة</div>
                        <div class="card-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="availableBackups">0</div>
                                <div class="stat-label">نسخة متاحة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-top: 30px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600;">النسخ الاحتياطية المتاحة</h2>
                        <div style="display: flex; gap: 10px;">
                            <button onclick="refreshBackupList()" style="background: #3498db; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">
                                <i class="fas fa-sync-alt" style="margin-left: 5px;"></i>
                                تحديث
                            </button>
                            <button onclick="cleanOldBackups()" style="background: #f39c12; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">
                                <i class="fas fa-broom" style="margin-left: 5px;"></i>
                                تنظيف القديمة
                            </button>
                        </div>
                    </div>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: rgba(52, 152, 219, 0.1);">
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">اسم النسخة</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">التاريخ والوقت</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الحجم</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">عدد الكتب</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">عدد الصور</th>
                                    <th style="padding: 15px; text-align: right; color: #2c3e50; font-weight: 600;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="backupTableBody">
                                <!-- سيتم تحديث المحتوى ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div style="background: rgba(22, 160, 133, 0.1); border-radius: 15px; padding: 20px; margin-top: 20px;">
                    <h3 style="color: #16a085; margin-bottom: 15px;">
                        <i class="fas fa-info-circle" style="margin-left: 8px;"></i>
                        معلومات مهمة عن النسخ الاحتياطي
                    </h3>
                    <ul style="color: #2c3e50; line-height: 1.8; margin-right: 20px;">
                        <li>يتم حفظ النسخ الاحتياطية في المجلد الذي تختاره</li>
                        <li>تحتوي النسخة على جميع بيانات الكتب والصور المرفقة</li>
                        <li>يمكن استعادة النسخة في أي وقت لاحق</li>
                        <li>ينصح بإنشاء نسخة احتياطية دورياً لحماية البيانات</li>
                        <li>مسار الحفظ الافتراضي: D:/ALI-AJIL-Arshef/</li>
                    </ul>
                </div>
            `;
        }

        // 13. محتوى الإعدادات
        function getSettingsContent() {
            return `
                <div style="margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; font-size: 32px; font-weight: 700; margin-bottom: 10px;">
                        <i class="fas fa-cog" style="color: #7f8c8d; margin-left: 15px;"></i>
                        إعدادات النظام
                    </h1>
                    <p style="color: #7f8c8d; font-size: 16px;">تخصيص شامل لإعدادات النظام والتفضيلات</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">
                    <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                        <h3 style="color: #2c3e50; font-size: 20px; font-weight: 600; margin-bottom: 20px;">
                            <i class="fas fa-palette" style="color: #e74c3c; margin-left: 10px;"></i>
                            إعدادات المظهر
                        </h3>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">نمط الألوان</label>
                            <select onchange="changeColorTheme(this.value)" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                                <option value="blue">الأزرق الافتراضي</option>
                                <option value="green">الأخضر الطبيعي</option>
                                <option value="purple">البنفسجي الملكي</option>
                                <option value="orange">البرتقالي الدافئ</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">حجم الخط</label>
                            <input type="range" min="12" max="20" value="16" onchange="changeFontSize(this.value)" style="width: 100%;">
                        </div>

                        <div>
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                <input type="checkbox" onchange="toggleDarkMode(this.checked)">
                                <span style="color: #2c3e50; font-weight: 600;">الوضع الليلي</span>
                            </label>
                        </div>
                    </div>

                    <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                        <h3 style="color: #2c3e50; font-size: 20px; font-weight: 600; margin-bottom: 20px;">
                            <i class="fas fa-book-open" style="color: #3498db; margin-left: 10px;"></i>
                            إعدادات المكتبة
                        </h3>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">مدة الاستعارة (أيام)</label>
                            <input type="number" value="14" min="1" max="90" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الحد الأقصى للكتب المستعارة</label>
                            <input type="number" value="5" min="1" max="20" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>

                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الغرامة اليومية</label>
                            <input type="number" value="1.00" step="0.50" min="0" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>
                    </div>

                    <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                        <h3 style="color: #2c3e50; font-size: 20px; font-weight: 600; margin-bottom: 20px;">
                            <i class="fas fa-bell" style="color: #f39c12; margin-left: 10px;"></i>
                            إعدادات الإشعارات
                        </h3>

                        <div style="margin-bottom: 15px;">
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                <input type="checkbox" checked>
                                <span style="color: #2c3e50; font-weight: 600;">إشعارات البريد الإلكتروني</span>
                            </label>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                <input type="checkbox" checked>
                                <span style="color: #2c3e50; font-weight: 600;">تذكير انتهاء الاستعارة</span>
                            </label>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                <input type="checkbox">
                                <span style="color: #2c3e50; font-weight: 600;">إشعارات الكتب الجديدة</span>
                            </label>
                        </div>

                        <div>
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                <input type="checkbox" checked>
                                <span style="color: #2c3e50; font-weight: 600;">تقارير دورية</span>
                            </label>
                        </div>
                    </div>

                    <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                        <h3 style="color: #2c3e50; font-size: 20px; font-weight: 600; margin-bottom: 20px;">
                            <i class="fas fa-shield-alt" style="color: #27ae60; margin-left: 10px;"></i>
                            إعدادات الأمان
                        </h3>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">مدة انتهاء الجلسة (دقيقة)</label>
                            <input type="number" value="30" min="5" max="480" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                <input type="checkbox" checked>
                                <span style="color: #2c3e50; font-weight: 600;">تسجيل العمليات</span>
                            </label>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                <input type="checkbox">
                                <span style="color: #2c3e50; font-weight: 600;">المصادقة الثنائية</span>
                            </label>
                        </div>

                        <button onclick="changePassword()" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; width: 100%;">
                            <i class="fas fa-key" style="margin-left: 8px;"></i>
                            تغيير كلمة المرور
                        </button>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button onclick="saveSettings()" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 15px 40px; border-radius: 10px; cursor: pointer; font-weight: 600; font-size: 16px; margin: 0 10px;">
                        <i class="fas fa-save" style="margin-left: 8px;"></i>
                        حفظ الإعدادات
                    </button>
                    <button onclick="resetSettings()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 15px 40px; border-radius: 10px; cursor: pointer; font-weight: 600; font-size: 16px; margin: 0 10px;">
                        <i class="fas fa-undo" style="margin-left: 8px;"></i>
                        إعادة تعيين
                    </button>
                </div>
            `;
        }

        // 14-42. الوظائف التفاعلية الـ 42

        // 14. وظائف الإشعارات
        function showNotifications() {
            showModal('الإشعارات', `
                <div style="max-height: 400px; overflow-y: auto;">
                    <div style="padding: 15px; border-bottom: 1px solid #ecf0f1; display: flex; align-items: center; gap: 15px;">
                        <div style="width: 40px; height: 40px; background: #3498db; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-book" style="color: white;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h4 style="color: #2c3e50; margin-bottom: 5px;">كتاب جديد متاح</h4>
                            <p style="color: #7f8c8d; font-size: 14px;">تم إضافة كتاب "الذكاء الاصطناعي" إلى المكتبة</p>
                            <small style="color: #95a5a6;">منذ 5 دقائق</small>
                        </div>
                    </div>
                    <div style="padding: 15px; border-bottom: 1px solid #ecf0f1; display: flex; align-items: center; gap: 15px;">
                        <div style="width: 40px; height: 40px; background: #e74c3c; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-exclamation-triangle" style="color: white;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h4 style="color: #2c3e50; margin-bottom: 5px;">كتاب متأخر</h4>
                            <p style="color: #7f8c8d; font-size: 14px;">يوجد كتاب متأخر في النظام</p>
                            <small style="color: #95a5a6;">منذ ساعة</small>
                        </div>
                    </div>
                    <div style="padding: 15px; display: flex; align-items: center; gap: 15px;">
                        <div style="width: 40px; height: 40px; background: #27ae60; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-check" style="color: white;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h4 style="color: #2c3e50; margin-bottom: 5px;">تم إنشاء نسخة احتياطية</h4>
                            <p style="color: #7f8c8d; font-size: 14px;">تم إنشاء النسخة الاحتياطية بنجاح</p>
                            <small style="color: #95a5a6;">منذ 3 ساعات</small>
                        </div>
                    </div>
                </div>
            `);
        }

        // 15. وظيفة عرض قائمة المستخدم
        function showUserMenu() {
            showModal('قائمة المستخدم', `
                <div style="text-align: center;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #3498db, #2980b9); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: 700;">
                        أ.م
                    </div>
                    <h3 style="color: #2c3e50; margin-bottom: 5px;">مدير النظام</h3>
                    <p style="color: #7f8c8d; margin-bottom: 30px;">نظام إدارة المكتبة الاحترافي</p>

                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <button onclick="editProfile()" style="background: #3498db; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-user-edit" style="margin-left: 8px;"></i>
                            تعديل الملف الشخصي
                        </button>
                        <button onclick="changePassword()" style="background: #f39c12; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-key" style="margin-left: 8px;"></i>
                            تغيير كلمة المرور
                        </button>
                        <button onclick="logout()" style="background: #e74c3c; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-sign-out-alt" style="margin-left: 8px;"></i>
                            تسجيل الخروج
                        </button>
                    </div>
                </div>
            `);
        }

        // 16. نظام المودال العام
        function showModal(title, content, size = 'medium') {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: all 0.3s ease;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 20px;
                padding: 30px;
                max-width: ${size === 'large' ? '800px' : size === 'small' ? '400px' : '600px'};
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                transform: scale(0.8);
                transition: all 0.3s ease;
            `;

            modalContent.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #ecf0f1;">
                    <h2 style="color: #2c3e50; font-size: 24px; font-weight: 600; margin: 0;">${title}</h2>
                    <button onclick="closeModal()" style="background: none; border: none; font-size: 24px; color: #7f8c8d; cursor: pointer; padding: 5px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div>${content}</div>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            currentModal = modal;

            // إظهار المودال
            setTimeout(() => {
                modal.style.opacity = '1';
                modalContent.style.transform = 'scale(1)';
            }, 10);

            // إغلاق عند النقر خارج المودال
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });
        }

        // 17. إغلاق المودال
        function closeModal() {
            if (currentModal) {
                currentModal.style.opacity = '0';
                currentModal.querySelector('div').style.transform = 'scale(0.8)';
                setTimeout(() => {
                    if (currentModal && currentModal.parentElement) {
                        currentModal.remove();
                    }
                    currentModal = null;
                }, 300);
            }
        }

        // 18. شريط التقدم
        function showProgressBar(title = 'جاري المعالجة...', duration = 3000) {
            const progressContainer = document.createElement('div');
            progressContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 15px;
                padding: 30px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 10001;
                min-width: 300px;
                text-align: center;
            `;

            progressContainer.innerHTML = `
                <h3 style="color: #2c3e50; margin-bottom: 20px;">${title}</h3>
                <div style="background: #ecf0f1; border-radius: 10px; height: 8px; overflow: hidden; margin-bottom: 15px;">
                    <div id="progressBarFill" style="background: linear-gradient(45deg, #3498db, #2980b9); height: 100%; width: 0%; transition: width 0.3s ease; border-radius: 10px;"></div>
                </div>
                <div id="progressText" style="color: #7f8c8d; font-size: 14px;">0%</div>
            `;

            document.body.appendChild(progressContainer);
            progressBar = progressContainer;

            // تحديث شريط التقدم
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                document.getElementById('progressBarFill').style.width = progress + '%';
                document.getElementById('progressText').textContent = Math.round(progress) + '%';

                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        if (progressBar && progressBar.parentElement) {
                            progressBar.remove();
                        }
                        progressBar = null;
                    }, 500);
                }
            }, duration / 20);
        }

        // 19-25. وظائف إدارة الكتب
        function addNewBook() {
            showModal('إضافة كتاب جديد', `
                <form onsubmit="saveNewBook(event)" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div>
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">عنوان الكتاب *</label>
                        <input type="text" id="bookTitle" required style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                    </div>
                    <div>
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">المؤلف *</label>
                        <input type="text" id="bookAuthor" required style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                    </div>
                    <div>
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">ISBN</label>
                        <input type="text" id="bookISBN" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                    </div>
                    <div>
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الفئة</label>
                        <select id="bookCategory" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                            <option value="">اختر الفئة</option>
                            <option value="literature">الأدب العربي</option>
                            <option value="science">العلوم</option>
                            <option value="history">التاريخ</option>
                            <option value="religion">الدين</option>
                            <option value="technology">التكنولوجيا</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الناشر</label>
                        <input type="text" id="bookPublisher" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                    </div>
                    <div>
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">سنة النشر</label>
                        <input type="number" id="bookYear" min="1900" max="2024" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                    </div>
                    <div style="grid-column: 1 / -1;">
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الوصف</label>
                        <textarea id="bookDescription" rows="3" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; resize: vertical;"></textarea>
                    </div>
                    <div style="grid-column: 1 / -1; display: flex; gap: 15px; justify-content: center; margin-top: 20px;">
                        <button type="submit" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-save" style="margin-left: 8px;"></i>
                            حفظ الكتاب
                        </button>
                        <button type="button" onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `, 'large');
        }

        function saveNewBook(event) {
            event.preventDefault();
            showProgressBar('جاري حفظ الكتاب...', 2000);
            closeModal();
            setTimeout(() => {
                showAlert('تم حفظ الكتاب بنجاح!', 'success');
            }, 2000);
        }

        function editBook(bookId) {
            showModal('تعديل بيانات الكتاب', `
                <p style="color: #7f8c8d; margin-bottom: 20px;">تعديل بيانات الكتاب رقم: ${bookId}</p>
                <div style="text-align: center;">
                    <button onclick="closeModal(); showAlert('تم تحديث بيانات الكتاب', 'success')" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-save" style="margin-left: 8px;"></i>
                        حفظ التعديلات
                    </button>
                </div>
            `);
        }

        function deleteBook(bookId) {
            showModal('تأكيد الحذف', `
                <div style="text-align: center;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #e74c3c; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">هل أنت متأكد من حذف هذا الكتاب؟</h3>
                    <p style="color: #7f8c8d; margin-bottom: 30px;">لا يمكن التراجع عن هذا الإجراء</p>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="confirmDeleteBook(${bookId})" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-trash" style="margin-left: 8px;"></i>
                            تأكيد الحذف
                        </button>
                        <button onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            `);
        }

        function confirmDeleteBook(bookId) {
            closeModal();
            showProgressBar('جاري حذف الكتاب...', 1500);
            setTimeout(() => {
                showAlert('تم حذف الكتاب بنجاح', 'success');
            }, 1500);
        }

        function viewBook(bookId) {
            showModal('تفاصيل الكتاب', `
                <div style="display: grid; grid-template-columns: 200px 1fr; gap: 30px;">
                    <div style="text-align: center;">
                        <div style="width: 150px; height: 200px; background: linear-gradient(45deg, #3498db, #2980b9); border-radius: 10px; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 48px;">
                            <i class="fas fa-book"></i>
                        </div>
                        <button style="background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-weight: 600; width: 100%; margin-bottom: 10px;">
                            <i class="fas fa-download" style="margin-left: 5px;"></i>
                            تحميل الغلاف
                        </button>
                        <button style="background: #f39c12; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-weight: 600; width: 100%;">
                            <i class="fas fa-qrcode" style="margin-left: 5px;"></i>
                            طباعة الباركود
                        </button>
                    </div>
                    <div>
                        <h3 style="color: #2c3e50; margin-bottom: 20px;">البرمجة بلغة Python</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div><strong>المؤلف:</strong> د. محمد علي</div>
                            <div><strong>ISBN:</strong> 978-123-456-789</div>
                            <div><strong>الفئة:</strong> التكنولوجيا</div>
                            <div><strong>الناشر:</strong> دار المعرفة</div>
                            <div><strong>سنة النشر:</strong> 2023</div>
                            <div><strong>عدد الصفحات:</strong> 450</div>
                            <div><strong>الحالة:</strong> <span style="color: #27ae60; font-weight: 600;">متاح</span></div>
                            <div><strong>الموقع:</strong> الرف A-15</div>
                        </div>
                        <div style="margin-top: 20px;">
                            <strong>الوصف:</strong>
                            <p style="color: #7f8c8d; line-height: 1.6; margin-top: 10px;">
                                كتاب شامل يغطي أساسيات البرمجة بلغة Python من المستوى المبتدئ إلى المتقدم،
                                يتضمن أمثلة عملية ومشاريع تطبيقية متنوعة.
                            </p>
                        </div>
                    </div>
                </div>
            `, 'large');
        }

        function importBooks() {
            showModal('استيراد الكتب', `
                <div style="text-align: center;">
                    <i class="fas fa-upload" style="font-size: 48px; color: #3498db; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">اختر ملف الاستيراد</h3>
                    <p style="color: #7f8c8d; margin-bottom: 30px;">يدعم: Excel (.xlsx), CSV (.csv)</p>
                    <input type="file" accept=".xlsx,.csv" style="margin-bottom: 20px;">
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="startImport()" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-upload" style="margin-left: 8px;"></i>
                            بدء الاستيراد
                        </button>
                        <button onclick="downloadTemplate()" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-download" style="margin-left: 8px;"></i>
                            تحميل القالب
                        </button>
                    </div>
                </div>
            `);
        }

        function exportBooks() {
            showModal('تصدير بيانات الكتب', `
                <div style="text-align: center;">
                    <i class="fas fa-download" style="font-size: 48px; color: #27ae60; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">اختر تنسيق التصدير</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 30px;">
                        <button onclick="exportToFormat('excel')" style="background: #27ae60; color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-file-excel" style="display: block; font-size: 24px; margin-bottom: 8px;"></i>
                            Excel
                        </button>
                        <button onclick="exportToFormat('csv')" style="background: #3498db; color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-file-csv" style="display: block; font-size: 24px; margin-bottom: 8px;"></i>
                            CSV
                        </button>
                        <button onclick="exportToFormat('pdf')" style="background: #e74c3c; color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-file-pdf" style="display: block; font-size: 24px; margin-bottom: 8px;"></i>
                            PDF
                        </button>
                    </div>
                </div>
            `);
        }

        // 26-30. وظائف المسح الضوئي
        function startBarcodeScanning() {
            showModal('مسح الباركود', `
                <div style="text-align: center;">
                    <div style="width: 300px; height: 200px; background: #ecf0f1; border: 3px dashed #bdc3c7; border-radius: 15px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                        <i class="fas fa-qrcode" style="font-size: 48px; color: #7f8c8d; margin-bottom: 15px;"></i>
                        <p style="color: #7f8c8d;">ضع الباركود أمام الكاميرا</p>
                    </div>
                    <button onclick="simulateScan()" style="background: linear-gradient(45deg, #9b59b6, #8e44ad); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-camera" style="margin-left: 8px;"></i>
                        بدء المسح
                    </button>
                </div>
            `);
        }

        function startImageScanning() {
            showModal('مسح الصور', `
                <div style="text-align: center;">
                    <div id="imageDropZone" style="width: 100%; height: 200px; background: #ecf0f1; border: 3px dashed #bdc3c7; border-radius: 15px; margin-bottom: 20px; display: flex; align-items: center; justify-content: center; flex-direction: column; cursor: pointer;" onclick="document.getElementById('imageInput').click()">
                        <i class="fas fa-image" style="font-size: 48px; color: #7f8c8d; margin-bottom: 15px;"></i>
                        <p style="color: #7f8c8d;">انقر لاختيار الصور أو اسحبها هنا</p>
                    </div>
                    <input type="file" id="imageInput" multiple accept="image/*" style="display: none;" onchange="handleImageUpload(this.files)">
                    <button onclick="processImages()" style="background: linear-gradient(45deg, #1abc9c, #16a085); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-cogs" style="margin-left: 8px;"></i>
                        معالجة الصور
                    </button>
                </div>
            `);
        }

        function startMultiScanning() {
            showProgressBar('جاري تهيئة المسح المتعدد...', 2000);
            setTimeout(() => {
                showAlert('تم تفعيل وضع المسح المتعدد', 'success');
            }, 2000);
        }

        function convertToPDF() {
            showModal('تحويل إلى PDF', `
                <div style="text-align: center;">
                    <i class="fas fa-file-pdf" style="font-size: 48px; color: #e74c3c; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">إعدادات التحويل</h3>
                    <div style="text-align: right; margin-bottom: 20px;">
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">جودة الصورة</label>
                        <select style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                            <option>عالية (300 DPI)</option>
                            <option>متوسطة (150 DPI)</option>
                            <option>منخفضة (72 DPI)</option>
                        </select>
                    </div>
                    <button onclick="startPDFConversion()" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-file-export" style="margin-left: 8px;"></i>
                        بدء التحويل
                    </button>
                </div>
            `);
        }

        function simulateScan() {
            closeModal();
            showProgressBar('جاري مسح الباركود...', 2000);
            setTimeout(() => {
                showAlert('تم العثور على الكتاب: البرمجة بلغة Python', 'success');
            }, 2000);
        }

        // 31-35. وظائف البحث المتقدم
        function performAdvancedSearch() {
            const searchByTitle = document.getElementById('searchByTitle').value;
            const searchByExecution = document.getElementById('searchByExecution').value;
            const fromDay = document.getElementById('fromDay').value;
            const fromMonth = document.getElementById('fromMonth').value;
            const fromYear = document.getElementById('fromYear').value;
            const toDay = document.getElementById('toDay').value;
            const toMonth = document.getElementById('toMonth').value;
            const toYear = document.getElementById('toYear').value;
            const searchByDetails = document.getElementById('searchByDetails').value;

            showProgressBar('جاري البحث في قاعدة البيانات...', 2000);
            
            setTimeout(() => {
                // البحث في البيانات المحفوظة
                let savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
                let results = savedBooks.filter(book => {
                    let matches = true;
                    
                    if (searchByTitle && !book.subject.toLowerCase().includes(searchByTitle.toLowerCase())) {
                        matches = false;
                    }
                    
                    if (searchByExecution && !book.execution.toLowerCase().includes(searchByExecution.toLowerCase())) {
                        matches = false;
                    }
                    
                    if (searchByDetails && !book.details.toLowerCase().includes(searchByDetails.toLowerCase())) {
                        matches = false;
                    }
                    
                    // فلترة التاريخ
                    if (fromDay && fromMonth && fromYear) {
                        const bookDate = new Date(book.date);
                        const fromDate = new Date(fromYear, fromMonth - 1, fromDay);
                        if (bookDate < fromDate) matches = false;
                    }
                    
                    if (toDay && toMonth && toYear) {
                        const bookDate = new Date(book.date);
                        const toDate = new Date(toYear, toMonth - 1, toDay);
                        if (bookDate > toDate) matches = false;
                    }
                    
                    return matches;
                });
                
                displaySearchResults(results);
                showAlert(`تم العثور على ${results.length} نتيجة`, 'success');
            }, 2000);
        }

        function displaySearchResults(results) {
            document.getElementById('searchResults').style.display = 'block';
            document.getElementById('resultsCount').textContent = `${results.length} نتيجة`;
            
            const tbody = document.getElementById('searchResultsBody');
            
            if (results.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="padding: 40px; text-align: center; color: #7f8c8d;">
                            <i class="fas fa-search-minus" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px; display: block;"></i>
                            <p style="font-size: 16px; margin-bottom: 10px;">لم يتم العثور على نتائج</p>
                            <p style="font-size: 14px; color: #95a5a6;">جرب تعديل معايير البحث</p>
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = results.map((book, index) => `
                <tr style="border-bottom: 1px solid #ecf0f1; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#f8f9fa'" onmouseout="this.style.backgroundColor='white'">
                    <td style="padding: 15px; color: #2c3e50; font-weight: 600;">${book.number}</td>
                    <td style="padding: 15px; color: #7f8c8d;">${book.date}</td>
                    <td style="padding: 15px; color: #2c3e50; font-weight: 500;">${book.subject}</td>
                    <td style="padding: 15px; color: #7f8c8d; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${book.details}">${book.details}</td>
                    <td style="padding: 15px; color: #7f8c8d; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${book.execution}">${book.execution}</td>
                    <td style="padding: 15px;">
                        <button onclick="viewBookDetails('${book.number}')" style="background: #3498db; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 0 2px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#2980b9'" onmouseout="this.style.backgroundColor='#3498db'">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editBookData('${book.number}')" style="background: #f39c12; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 0 2px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#e67e22'" onmouseout="this.style.backgroundColor='#f39c12'">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteBookData('${book.number}')" style="background: #e74c3c; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 0 2px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#c0392b'" onmouseout="this.style.backgroundColor='#e74c3c'">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function clearAdvancedSearchForm() {
            document.getElementById('searchByTitle').value = '';
            document.getElementById('searchByExecution').value = '';
            document.getElementById('fromDay').value = '';
            document.getElementById('fromMonth').value = '';
            document.getElementById('fromYear').value = '';
            document.getElementById('toDay').value = '';
            document.getElementById('toMonth').value = '';
            document.getElementById('toYear').value = '';
            document.getElementById('searchByDetails').value = '';
            document.getElementById('searchResults').style.display = 'none';
            showAlert('تم مسح جميع حقول البحث', 'info');
        }

        function clearSearchForm() {
            document.getElementById('searchTitle').value = '';
            document.getElementById('searchAuthor').value = '';
            document.getElementById('searchCategory').value = '';
            document.getElementById('yearFrom').value = '';
            document.getElementById('yearTo').value = '';
            document.getElementById('searchStatus').value = '';
            document.getElementById('searchKeywords').value = '';
            showAlert('تم مسح جميع الحقول', 'info');
        }

        function saveSearchTemplate() {
            showModal('حفظ قالب البحث', `
                <div style="text-align: center;">
                    <input type="text" placeholder="اسم القالب..." style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; margin-bottom: 20px;">
                    <button onclick="closeModal(); showAlert('تم حفظ القالب بنجاح', 'success')" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-save" style="margin-left: 8px;"></i>
                        حفظ القالب
                    </button>
                </div>
            `);
        }

        // 36-42. وظائف متنوعة
        function quickAddBook() {
            showModal('إضافة سريعة', `
                <div style="text-align: center;">
                    <i class="fas fa-plus-circle" style="font-size: 48px; color: #27ae60; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">إضافة كتاب سريعة</h3>
                    <input type="text" placeholder="عنوان الكتاب..." style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; margin-bottom: 15px;">
                    <input type="text" placeholder="اسم المؤلف..." style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; margin-bottom: 20px;">
                    <button onclick="closeModal(); showAlert('تم إضافة الكتاب بنجاح', 'success')" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-save" style="margin-left: 8px;"></i>
                        إضافة
                    </button>
                </div>
            `);
        }

        function quickScan() {
            showProgressBar('جاري المسح السريع...', 1500);
            setTimeout(() => {
                showAlert('تم المسح بنجاح', 'success');
            }, 1500);
        }

        function quickSearch() {
            showModal('بحث سريع', `
                <div style="text-align: center;">
                    <input type="text" placeholder="ابحث عن كتاب..." style="width: 100%; padding: 15px 20px; border: 2px solid #ecf0f1; border-radius: 25px; outline: none; font-size: 16px; margin-bottom: 20px;">
                    <button onclick="closeModal(); showAlert('تم العثور على 5 نتائج', 'info')" style="background: linear-gradient(45deg, #f39c12, #e67e22); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-search" style="margin-left: 8px;"></i>
                        بحث
                    </button>
                </div>
            `);
        }

        function logout() {
            showModal('تأكيد تسجيل الخروج', `
                <div style="text-align: center;">
                    <i class="fas fa-sign-out-alt" style="font-size: 48px; color: #e74c3c; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">هل تريد تسجيل الخروج؟</h3>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="confirmLogout()" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-check" style="margin-left: 8px;"></i>
                            تأكيد
                        </button>
                        <button onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            `);
        }

        function confirmLogout() {
            closeModal();
            showProgressBar('جاري تسجيل الخروج...', 1500);
            setTimeout(() => {
                showAlert('تم تسجيل الخروج بنجاح', 'success');
            }, 1500);
        }

        // وظائف إضافية للتفاعل
        function handleDrop(event) {
            event.preventDefault();
            const files = event.dataTransfer.files;
            handleFileSelect({target: {files: files}});
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.target.style.borderColor = '#3498db';
            event.target.style.background = 'rgba(52, 152, 219, 0.1)';
        }

        function handleDragLeave(event) {
            event.target.style.borderColor = '#bdc3c7';
            event.target.style.background = 'transparent';
        }

        function handleFileSelect(event) {
            const files = event.target.files;
            if (files.length > 0) {
                showProgressBar('جاري معالجة الملفات...', 2000);
                setTimeout(() => {
                    showAlert(`تم رفع ${files.length} ملف بنجاح`, 'success');
                }, 2000);
            }
        }

        // تفعيل منطقة الرفع
        document.addEventListener('DOMContentLoaded', function() {
            const dropZone = document.getElementById('dropZone');
            if (dropZone) {
                dropZone.addEventListener('click', () => {
                    document.getElementById('fileInput').click();
                });
            }
        });

        // إضافة تأثيرات الحركة للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.section-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });

        // تحديث الوقت في الشريط العلوي
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            // يمكن إضافة عنصر لعرض الوقت في الشريط العلوي
        }

        setInterval(updateTime, 1000);

        // وظيفة تبديل الشريط الجانبي للهواتف المحمولة
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // إظهار/إخفاء زر القائمة للهواتف المحمولة
        function checkScreenSize() {
            const mobileToggle = document.getElementById('mobileMenuToggle');
            if (window.innerWidth <= 768) {
                mobileToggle.style.display = 'block';
            } else {
                mobileToggle.style.display = 'none';
                document.getElementById('sidebar').classList.remove('mobile-open');
            }
        }

        // تحقق من حجم الشاشة عند تحميل الصفحة وتغيير الحجم
        window.addEventListener('load', checkScreenSize);
        window.addEventListener('resize', checkScreenSize);

        // إغلاق الشريط الجانبي عند النقر خارجه على الهواتف المحمولة
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const mobileToggle = document.getElementById('mobileMenuToggle');

            if (window.innerWidth <= 768 &&
                sidebar.classList.contains('mobile-open') &&
                !sidebar.contains(event.target) &&
                !mobileToggle.contains(event.target)) {
                sidebar.classList.remove('mobile-open');
            }
        });

        // تحسين الأداء - تحميل البيانات بشكل تدريجي
        function lazyLoadContent() {
            // يمكن إضافة تحميل تدريجي للمحتوى هنا
            console.log('تم تفعيل التحميل التدريجي');
        }

        // تحسين تجربة المستخدم - حفظ حالة الشريط الجانبي
        function saveUIState() {
            localStorage.setItem('sidebarCollapsed', sidebarCollapsed);
            localStorage.setItem('currentSection', currentSection);
        }

        function loadUIState() {
            const savedCollapsed = localStorage.getItem('sidebarCollapsed');
            const savedSection = localStorage.getItem('currentSection');

            if (savedCollapsed === 'true') {
                toggleSidebar();
            }

            if (savedSection && savedSection !== 'dashboard') {
                showSection(savedSection);
            }
        }

        // تحميل الحالة المحفوظة عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            loadUIState();
            lazyLoadContent();
        });

        // حفظ الحالة عند إغلاق الصفحة
        window.addEventListener('beforeunload', saveUIState);

        // وظائف حساب الإحصائيات
        function updateDashboardStats() {
            const savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
            const savedUsers = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            const transactions = JSON.parse(localStorage.getItem('transactions') || '[]');
            
            // إحصائيات الكتب
            document.getElementById('totalBooks').textContent = savedBooks.length;
            document.getElementById('availableBooks').textContent = savedBooks.filter(book => book.status === 'available').length || savedBooks.length;
            document.getElementById('borrowedBooks').textContent = savedBooks.filter(book => book.status === 'borrowed').length;
            
            // إحصائيات المستخدمين
            document.getElementById('totalUsers').textContent = savedUsers.length;
            document.getElementById('activeUsers').textContent = savedUsers.filter(user => user.status === 'active').length;
            document.getElementById('newUsers').textContent = savedUsers.filter(user => {
                const userDate = new Date(user.createdAt);
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                return userDate > weekAgo;
            }).length;
            
            // إحصائيات المعاملات
            const today = new Date().toDateString();
            const thisWeek = new Date();
            thisWeek.setDate(thisWeek.getDate() - 7);
            const thisMonth = new Date();
            thisMonth.setMonth(thisMonth.getMonth() - 1);
            
            document.getElementById('todayTransactions').textContent = transactions.filter(t => new Date(t.date).toDateString() === today).length;
            document.getElementById('weekTransactions').textContent = transactions.filter(t => new Date(t.date) > thisWeek).length;
            document.getElementById('monthTransactions').textContent = transactions.filter(t => new Date(t.date) > thisMonth).length;
            
            // إحصائيات التنبيهات
            document.getElementById('overdueBooks').textContent = savedBooks.filter(book => book.status === 'overdue').length;
            document.getElementById('lostBooks').textContent = savedBooks.filter(book => book.status === 'lost').length;
            document.getElementById('damagedBooks').textContent = savedBooks.filter(book => book.status === 'damaged').length;
        }

        // وظائف جديدة للماسح الضوئي وحفظ البيانات
        async function startScanner() {
            try {
                showProgressBar('جاري الوصول لنظام المسح في Windows...', 2000);
                
                setTimeout(async () => {
                    try {
                        // محاولة استخدام Windows Scanner API
                        if (window.Windows && window.Windows.Devices && window.Windows.Devices.Scanners) {
                            const scanners = await Windows.Devices.Scanners.ImageScanner.getDeviceSelector();
                            const deviceInfo = await Windows.Devices.Enumeration.DeviceInformation.findAllAsync(scanners);
                            
                            if (deviceInfo.length > 0) {
                                const scanner = await Windows.Devices.Scanners.ImageScanner.fromIdAsync(deviceInfo[0].id);
                                showWindowsScannerInterface(scanner, deviceInfo[0].name);
                            } else {
                                throw new Error('No scanners found');
                            }
                        } else {
                            // استخدام WIA (Windows Image Acquisition)
                            if (window.ActiveXObject || 'ActiveXObject' in window) {
                                const wia = new ActiveXObject('WIA.CommonDialog');
                                const scanner = wia.ShowSelectDevice();
                                
                                if (scanner) {
                                    showWIAScannerInterface(scanner);
                                } else {
                                    throw new Error('No scanner selected');
                                }
                            } else {
                                // فتح برنامج المسح الافتراضي في Windows
                                const shell = new ActiveXObject('WScript.Shell');
                                shell.Run('ms-settings:printers', 1, false);
                                
                                setTimeout(() => {
                                    shell.Run('WiaAcmgr.exe', 1, false); // Windows Image Acquisition
                                }, 1000);
                                
                                showSystemScannerInterface();
                            }
                        }
                        
                    } catch (error) {
                        console.log('Trying alternative scanner methods...');
                        
                        // طريقة بديلة - فتح برنامج المسح في Windows
                        try {
                            if (window.ActiveXObject || 'ActiveXObject' in window) {
                                const shell = new ActiveXObject('WScript.Shell');
                                // فتح برنامج Windows Fax and Scan
                                shell.Run('WFS.exe', 1, false);
                                showSystemScannerInterface();
                            } else {
                                // للمتصفحات الحديثة
                                showWebScannerInterface();
                            }
                        } catch (fallbackError) {
                            showManualScanOptions();
                        }
                    }
                }, 2000);
                
            } catch (error) {
                showAlert('خطأ في الوصول لنظام المسح', 'error');
                showManualScanOptions();
            }
        }

        function showSystemScannerInterface() {
            showModal('نظام المسح Windows', `
                <div style="text-align: center;">
                    <div style="width: 400px; height: 300px; background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); border-radius: 15px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; position: relative; color: white;">
                        <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                            <i class="fab fa-windows" style="font-size: 64px; margin-bottom: 20px; animation: pulse 2s infinite;"></i>
                            <p style="font-weight: 700; font-size: 18px; margin-bottom: 10px;">نظام المسح Windows</p>
                            <p style="font-size: 14px; opacity: 0.9;">تم فتح برنامج المسح الافتراضي</p>
                            <div style="margin-top: 15px; padding: 8px 15px; background: rgba(255,255,255,0.2); border-radius: 20px; font-size: 12px;">
                                Windows Image Acquisition
                            </div>
                        </div>
                    </div>
                    <div style="background: rgba(76, 175, 80, 0.1); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <p style="color: #2c3e50; margin-bottom: 15px; line-height: 1.6;">
                            <i class="fas fa-info-circle" style="color: #4CAF50; margin-left: 8px;"></i>
                            تم فتح برنامج المسح في Windows. قم بالخطوات التالية:
                        </p>
                        <ol style="text-align: right; color: #555; line-height: 2;">
                            <li>اختر الماسح الضوئي من القائمة</li>
                            <li>ضع المستند في الماسح</li>
                            <li>اضغط على "مسح" في البرنامج</li>
                            <li>احفظ الصورة في مجلد معروف</li>
                        </ol>
                    </div>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="waitForScanCompletion()" style="background: linear-gradient(45deg, #4CAF50, #45a049); color: white; border: none; padding: 15px 35px; border-radius: 10px; cursor: pointer; font-weight: 600; font-size: 16px;">
                            <i class="fas fa-check" style="margin-left: 8px;"></i>
                            تم المسح
                        </button>
                        <button onclick="openWindowsScanner()" style="background: linear-gradient(45deg, #2196F3, #1976D2); color: white; border: none; padding: 15px 35px; border-radius: 10px; cursor: pointer; font-weight: 600; font-size: 16px;">
                            <i class="fas fa-external-link-alt" style="margin-left: 8px;"></i>
                            فتح الماسح
                        </button>
                        <button onclick="closeModal()" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; padding: 15px 35px; border-radius: 10px; cursor: pointer; font-weight: 600; font-size: 16px;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `, 'large');
        }

        function showWebScannerInterface() {
            showModal('ماسح الويب', `
                <div style="text-align: center;">
                    <div style="width: 400px; height: 250px; background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%); border-radius: 15px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white;">
                        <div style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
                            <i class="fas fa-globe" style="font-size: 64px; margin-bottom: 20px;"></i>
                            <p style="font-weight: 700; font-size: 18px; margin-bottom: 10px;">ماسح الويب</p>
                            <p style="font-size: 14px; opacity: 0.9;">استخدام كاميرا الويب للمسح</p>
                        </div>
                    </div>
                    <button onclick="startWebCameraScanning()" style="background: linear-gradient(45deg, #FF9800, #F57C00); color: white; border: none; padding: 15px 35px; border-radius: 10px; cursor: pointer; font-weight: 600; font-size: 16px; margin: 10px;">
                        <i class="fas fa-camera" style="margin-left: 8px;"></i>
                        بدء مسح بالكاميرا
                    </button>
                </div>
            `);
        }

        function openWindowsScanner() {
            try {
                if (window.ActiveXObject || 'ActiveXObject' in window) {
                    const shell = new ActiveXObject('WScript.Shell');
                    // جرب عدة برامج مسح
                    shell.Run('WiaAcmgr.exe', 1, false); // Windows Image Acquisition Manager
                    setTimeout(() => {
                        shell.Run('WFS.exe', 1, false); // Windows Fax and Scan
                    }, 500);
                    showAlert('تم فتح برنامج المسح', 'success');
                } else {
                    showAlert('يرجى فتح برنامج المسح يدوياً', 'info');
                }
            } catch (error) {
                showAlert('تعذر فتح برنامج المسح', 'warning');
            }
        }

        // وظائف البحث المتقدم - المعاينة والتعديل والحذف

        // معاينة تفاصيل الكتاب مع الصور
        function viewBookDetails(bookNumber) {
            const savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
            const book = savedBooks.find(b => b.number === bookNumber);

            if (!book) {
                showAlert('لم يتم العثور على الكتاب', 'error');
                return;
            }

            let imagesHtml = '';
            if (book.images && book.images.length > 0) {
                imagesHtml = `
                    <div style="margin-top: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">
                            <i class="fas fa-images" style="color: #3498db; margin-left: 8px;"></i>
                            الصور المرفقة (${book.images.length})
                        </h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px; max-height: 300px; overflow-y: auto;">
                            ${book.images.map((image, index) => `
                                <div style="position: relative; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                    <img src="${image.data}" style="width: 100%; height: 120px; object-fit: cover; cursor: pointer;" onclick="showFullImage('${image.data}', '${image.name}')">
                                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 8px; font-size: 12px; text-align: center;">
                                        ${image.name}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else {
                imagesHtml = `
                    <div style="margin-top: 20px; text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                        <i class="fas fa-image" style="font-size: 32px; color: #bdc3c7; margin-bottom: 10px;"></i>
                        <p style="color: #7f8c8d;">لا توجد صور مرفقة مع هذا الكتاب</p>
                    </div>
                `;
            }

            showModal('تفاصيل الكتاب', `
                <div style="max-height: 600px; overflow-y: auto;">
                    <div style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                        <h2 style="margin: 0; font-size: 24px; font-weight: 700;">
                            <i class="fas fa-book-open" style="margin-left: 10px;"></i>
                            ${book.subject || 'بدون عنوان'}
                        </h2>
                        <p style="margin: 10px 0 0 0; opacity: 0.9;">رقم الكتاب: ${book.number}</p>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">
                                <i class="fas fa-calendar" style="color: #e74c3c; margin-left: 8px;"></i>
                                التاريخ
                            </h4>
                            <p style="color: #7f8c8d; margin: 0;">${book.day || ''}/${book.month || ''}/${book.year || ''}</p>
                        </div>

                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">
                                <i class="fas fa-clock" style="color: #f39c12; margin-left: 8px;"></i>
                                تاريخ الإضافة
                            </h4>
                            <p style="color: #7f8c8d; margin: 0;">${new Date(book.timestamp).toLocaleDateString('ar-SA')}</p>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">
                            <i class="fas fa-align-left" style="color: #27ae60; margin-left: 8px;"></i>
                            التفاصيل
                        </h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-right: 4px solid #27ae60;">
                            <p style="color: #2c3e50; line-height: 1.6; margin: 0;">${book.details || 'لا توجد تفاصيل'}</p>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">
                            <i class="fas fa-cogs" style="color: #9b59b6; margin-left: 8px;"></i>
                            التنفيذ
                        </h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-right: 4px solid #9b59b6;">
                            <p style="color: #2c3e50; line-height: 1.6; margin: 0;">${book.execution || 'لا يوجد تنفيذ'}</p>
                        </div>
                    </div>

                    ${imagesHtml}
                </div>

                <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 2px solid #ecf0f1;">
                    <button onclick="editBookFromSearch('${book.number}')" style="background: linear-gradient(45deg, #f39c12, #e67e22); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600; margin: 0 10px;">
                        <i class="fas fa-edit" style="margin-left: 8px;"></i>
                        تعديل البيانات
                    </button>
                    <button onclick="printBookDetails('${book.number}')" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600; margin: 0 10px;">
                        <i class="fas fa-print" style="margin-left: 8px;"></i>
                        طباعة
                    </button>
                </div>
            `, 'large');
        }

        // عرض الصورة بالحجم الكامل
        function showFullImage(imageSrc, imageName) {
            showModal('عرض الصورة', `
                <div style="text-align: center;">
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">${imageName}</h3>
                    <img src="${imageSrc}" style="max-width: 100%; max-height: 500px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div style="margin-top: 20px;">
                        <button onclick="downloadImage('${imageSrc}', '${imageName}')" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600; margin: 0 10px;">
                            <i class="fas fa-download" style="margin-left: 8px;"></i>
                            تحميل الصورة
                        </button>
                    </div>
                </div>
            `, 'large');
        }

        // تحميل الصورة
        function downloadImage(imageSrc, imageName) {
            const link = document.createElement('a');
            link.href = imageSrc;
            link.download = imageName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            showAlert('تم تحميل الصورة', 'success');
        }

        // تعديل بيانات الكتاب من البحث
        function editBookFromSearch(bookNumber) {
            const savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
            const book = savedBooks.find(b => b.number === bookNumber);

            if (!book) {
                showAlert('لم يتم العثور على الكتاب', 'error');
                return;
            }

            closeModal(); // إغلاق المودال الحالي

            showModal('تعديل بيانات الكتاب', `
                <form onsubmit="saveEditedBook(event, '${bookNumber}')" style="max-height: 500px; overflow-y: auto;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">رقم الكتاب *</label>
                            <input type="text" id="editBookNumber" value="${book.number}" required style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>

                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">اليوم</label>
                            <input type="number" id="editBookDay" value="${book.day || ''}" min="1" max="31" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>

                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الشهر</label>
                            <input type="number" id="editBookMonth" value="${book.month || ''}" min="1" max="12" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>

                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">السنة</label>
                            <input type="number" id="editBookYear" value="${book.year || ''}" min="1900" max="2030" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الموضوع *</label>
                        <input type="text" id="editBookSubject" value="${book.subject || ''}" required style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">التفاصيل</label>
                        <textarea id="editBookDetails" rows="4" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; resize: vertical;">${book.details || ''}</textarea>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">التنفيذ</label>
                        <textarea id="editBookExecution" rows="3" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; resize: vertical;">${book.execution || ''}</textarea>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الصور المرفقة</label>
                        <div id="editBookImages" style="min-height: 100px; border: 2px dashed #bdc3c7; border-radius: 10px; padding: 20px; text-align: center; background: #f8f9fa;">
                            ${book.images && book.images.length > 0 ?
                                `<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); gap: 10px;">
                                    ${book.images.map((img, index) => `
                                        <div style="position: relative;">
                                            <img src="${img.data}" style="width: 100%; height: 60px; object-fit: cover; border-radius: 5px;">
                                            <button type="button" onclick="removeEditImage(${index})" style="position: absolute; top: -5px; right: -5px; background: #e74c3c; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; font-size: 12px; cursor: pointer;">×</button>
                                        </div>
                                    `).join('')}
                                </div>
                                <p style="color: #27ae60; margin-top: 10px;">عدد الصور: ${book.images.length}</p>`
                                : '<p style="color: #7f8c8d;">لا توجد صور مرفقة</p>'
                            }
                            <button type="button" onclick="addMoreImages()" style="background: #3498db; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; margin-top: 10px;">
                                <i class="fas fa-plus" style="margin-left: 5px;"></i>
                                إضافة صور
                            </button>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center; margin-top: 30px;">
                        <button type="submit" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-save" style="margin-left: 8px;"></i>
                            حفظ التعديلات
                        </button>
                        <button type="button" onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `, 'large');

            // حفظ الصور الحالية في متغير مؤقت
            window.currentEditImages = book.images || [];
        }

        // حفظ التعديلات
        function saveEditedBook(event, originalBookNumber) {
            event.preventDefault();

            const updatedBook = {
                number: document.getElementById('editBookNumber').value,
                day: document.getElementById('editBookDay').value,
                month: document.getElementById('editBookMonth').value,
                year: document.getElementById('editBookYear').value,
                subject: document.getElementById('editBookSubject').value,
                details: document.getElementById('editBookDetails').value,
                execution: document.getElementById('editBookExecution').value,
                images: window.currentEditImages || [],
                timestamp: new Date().toISOString(),
                lastModified: new Date().toISOString()
            };

            if (!updatedBook.number || !updatedBook.subject) {
                showAlert('يرجى ملء الحقول المطلوبة (رقم الكتاب والموضوع)', 'warning');
                return;
            }

            let savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');

            // العثور على الكتاب وتحديثه
            const bookIndex = savedBooks.findIndex(book => book.number === originalBookNumber);
            if (bookIndex !== -1) {
                savedBooks[bookIndex] = updatedBook;
                localStorage.setItem('archivedBooks', JSON.stringify(savedBooks));
                closeModal();
                performAdvancedSearch();
                updateDashboardStats();
                showAlert('تم تحديث بيانات الكتاب بنجاح', 'success');
            }
        }

        // حذف الكتاب من البحث
        function deleteBookFromSearch(bookNumber) {
            const savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
            const book = savedBooks.find(b => b.number === bookNumber);

            if (!book) {
                showAlert('لم يتم العثور على الكتاب', 'error');
                return;
            }

            showModal('تأكيد حذف الكتاب', `
                <div style="text-align: center;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 64px; color: #e74c3c; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">هل أنت متأكد من حذف هذا الكتاب؟</h3>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: right;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">بيانات الكتاب:</h4>
                        <p><strong>رقم الكتاب:</strong> ${book.number}</p>
                        <p><strong>الموضوع:</strong> ${book.subject}</p>
                        <p><strong>عدد الصور:</strong> ${book.images ? book.images.length : 0}</p>
                    </div>

                    <div style="background: rgba(231, 76, 60, 0.1); padding: 15px; border-radius: 10px; margin: 20px 0;">
                        <p style="color: #e74c3c; font-weight: 600; margin: 0;">
                            <i class="fas fa-warning" style="margin-left: 8px;"></i>
                            تحذير: سيتم حذف جميع البيانات والصور المرفقة نهائياً ولا يمكن التراجع عن هذا الإجراء
                        </p>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="confirmDeleteBook('${bookNumber}')" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-trash" style="margin-left: 8px;"></i>
                            تأكيد الحذف
                        </button>
                        <button onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            `);
        }

        // تأكيد حذف الكتاب
        function confirmDeleteBook(bookNumber) {
            let savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');

            // العثور على الكتاب
            const book = savedBooks.find(b => b.number === bookNumber);
            if (book && book.images) {
                // حذف الصور من الذاكرة (في التطبيق الحقيقي ستحذف من الخادم)
                console.log(`حذف ${book.images.length} صورة مرتبطة بالكتاب ${bookNumber}`);
            }

            // حذف بيانات الكتاب
            savedBooks = savedBooks.filter(book => book.number !== bookNumber);
            localStorage.setItem('archivedBooks', JSON.stringify(savedBooks));

            closeModal();
            performAdvancedSearch();
            updateDashboardStats();
            showAlert('تم حذف الكتاب وصوره بنجاح', 'success');
        }

        // وظائف النسخ الاحتياطي الحقيقية المتطورة

        // متغيرات النسخ الاحتياطي
        let backupList = [];
        const defaultBackupPath = 'D:/ALI-AJIL-Arshef/';

        // تحديث قائمة النسخ الاحتياطية
        function updateBackupList() {
            backupList = JSON.parse(localStorage.getItem('systemBackups') || '[]');

            // تحديث الإحصائيات
            const savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
            const totalImages = savedBooks.reduce((total, book) => total + (book.images ? book.images.length : 0), 0);
            const totalDataSize = calculateDataSize(savedBooks);

            setTimeout(() => {
                const totalDataElement = document.getElementById('totalDataSize');
                const totalImagesElement = document.getElementById('totalImages');
                const availableBackupsElement = document.getElementById('availableBackups');

                if (totalDataElement) totalDataElement.textContent = totalDataSize.toFixed(1);
                if (totalImagesElement) totalImagesElement.textContent = totalImages;
                if (availableBackupsElement) availableBackupsElement.textContent = backupList.length;

                updateBackupTable();
            }, 100);
        }

        // حساب حجم البيانات
        function calculateDataSize(books) {
            let totalSize = 0;
            books.forEach(book => {
                // حساب حجم النص
                const textSize = JSON.stringify(book).length;
                totalSize += textSize;

                // حساب حجم الصور
                if (book.images) {
                    book.images.forEach(image => {
                        totalSize += image.size || 0;
                    });
                }
            });
            return totalSize / (1024 * 1024); // تحويل إلى ميجابايت
        }

        // تحديث جدول النسخ الاحتياطية
        function updateBackupTable() {
            const tableBody = document.getElementById('backupTableBody');
            if (!tableBody) return;

            if (backupList.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td style="padding: 15px; color: #7f8c8d; text-align: center;" colspan="6">
                            <i class="fas fa-database" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px; display: block;"></i>
                            <p style="color: #95a5a6; font-size: 16px;">لا توجد نسخ احتياطية محفوظة</p>
                            <p style="color: #7f8c8d; font-size: 14px;">قم بإنشاء نسخة احتياطية لحماية بياناتك</p>
                        </td>
                    </tr>
                `;
                return;
            }

            let tableHtml = '';
            backupList.forEach((backup, index) => {
                const date = new Date(backup.timestamp);
                const formattedDate = date.toLocaleDateString('ar-SA') + ' - ' + date.toLocaleTimeString('ar-SA');

                tableHtml += `
                    <tr style="border-bottom: 1px solid rgba(0,0,0,0.1); ${index % 2 === 0 ? 'background: rgba(52, 152, 219, 0.05);' : ''}">
                        <td style="padding: 15px; color: #2c3e50; font-weight: 600;">${backup.name}</td>
                        <td style="padding: 15px; color: #7f8c8d;">${formattedDate}</td>
                        <td style="padding: 15px; color: #7f8c8d;">${backup.size} MB</td>
                        <td style="padding: 15px; color: #7f8c8d;">${backup.booksCount} كتاب</td>
                        <td style="padding: 15px; color: #7f8c8d;">${backup.imagesCount} صورة</td>
                        <td style="padding: 15px;">
                            <div style="display: flex; gap: 5px;">
                                <button onclick="restoreSpecificBackup('${backup.id}')" style="background: #3498db; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;" title="استعادة">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button onclick="downloadBackupFile('${backup.id}')" style="background: #27ae60; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button onclick="viewBackupDetails('${backup.id}')" style="background: #f39c12; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;" title="التفاصيل">
                                    <i class="fas fa-info"></i>
                                </button>
                                <button onclick="deleteBackupFile('${backup.id}')" style="background: #e74c3c; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            tableBody.innerHTML = tableHtml;
        }

        // إنشاء نسخة احتياطية حقيقية
        function createRealBackup() {
            showModal('إنشاء نسخة احتياطية', `
                <div style="text-align: center;">
                    <i class="fas fa-database" style="font-size: 64px; color: #16a085; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">إنشاء نسخة احتياطية شاملة</h3>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: right;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">محتويات النسخة الاحتياطية:</h4>
                        <ul style="color: #7f8c8d; line-height: 1.8; margin-right: 20px;">
                            <li>جميع بيانات الكتب المحفوظة</li>
                            <li>جميع الصور المرفقة مع الكتب</li>
                            <li>إعدادات النظام</li>
                            <li>سجل الأنشطة</li>
                            <li>النسخ الاحتياطية السابقة</li>
                        </ul>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">اسم النسخة الاحتياطية</label>
                        <input type="text" id="backupName" value="نسخة_احتياطية_${new Date().toISOString().split('T')[0]}" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">مكان الحفظ</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" id="backupPath" value="${defaultBackupPath}" style="flex: 1; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                            <button onclick="selectBackupPath()" style="background: #3498db; color: white; border: none; padding: 12px 15px; border-radius: 10px; cursor: pointer;">
                                <i class="fas fa-folder-open"></i>
                            </button>
                        </div>
                        <small style="color: #7f8c8d;">اختر المجلد الذي تريد حفظ النسخة الاحتياطية فيه</small>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="startBackupProcess()" style="background: linear-gradient(45deg, #16a085, #1abc9c); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-play" style="margin-left: 8px;"></i>
                            بدء النسخ الاحتياطي
                        </button>
                        <button onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            `, 'large');
        }

        function waitForScanCompletion() {
            closeModal();
            showProgressBar('انتظار اكتمال عملية المسح...', 3000);
            
            setTimeout(() => {
                const subject = document.getElementById('bookSubject').value || 'مسح_نظام';
                const timestamp = new Date().getTime();
                const imageName = `${subject}_${timestamp}.jpg`;
                
                showAlert(`تم استلام الصورة: ${imageName}`, 'success');
                showAlert('تم حفظ الصورة في D:/ALI-AJIL-Arshef/صور الارشيف/', 'info');
            }, 3000);
        }

        function startWebCameraScanning() {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(stream => {
                    showModal('مسح بالكاميرا', `
                        <div style="text-align: center;">
                            <video id="cameraVideo" autoplay style="width: 100%; max-width: 400px; border-radius: 10px; margin-bottom: 20px;"></video>
                            <div style="display: flex; gap: 15px; justify-content: center;">
                                <button onclick="captureImage()" style="background: #4CAF50; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer;">
                                    <i class="fas fa-camera" style="margin-left: 8px;"></i>
                                    التقاط صورة
                                </button>
                                <button onclick="stopCamera()" style="background: #f44336; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer;">
                                    إيقاف
                                </button>
                            </div>
                        </div>
                    `, 'large');
                    
                    document.getElementById('cameraVideo').srcObject = stream;
                })
                .catch(error => {
                    showAlert('تعذر الوصول للكاميرا', 'error');
                });
        }

        function showManualScanOptions() {
            showModal('خيارات المسح', `
                <div style="text-align: center;">
                    <i class="fas fa-tools" style="font-size: 48px; color: #607D8B; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">اختر طريقة المسح</h3>
                    <div style="display: grid; gap: 15px;">
                        <button onclick="manualScanMode()" style="background: #4CAF50; color: white; border: none; padding: 15px 25px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-upload" style="margin-left: 8px;"></i>
                            رفع صور من الجهاز
                        </button>
                        <button onclick="startWebCameraScanning()" style="background: #FF9800; color: white; border: none; padding: 15px 25px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-camera" style="margin-left: 8px;"></i>
                            استخدام الكاميرا
                        </button>
                    </div>
                </div>
            `);
        }

        function captureImage() {
            const video = document.getElementById('cameraVideo');
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0);
            
            const subject = document.getElementById('bookSubject').value || 'مسح_كاميرا';
            const timestamp = new Date().getTime();
            const imageName = `${subject}_${timestamp}.jpg`;
            
            // تحويل الصورة إلى blob
            canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = imageName;
                a.click();
                
                showAlert(`تم حفظ الصورة: ${imageName}`, 'success');
                stopCamera();
            }, 'image/jpeg', 0.9);
        }

        function stopCamera() {
            const video = document.getElementById('cameraVideo');
            if (video && video.srcObject) {
                video.srcObject.getTracks().forEach(track => track.stop());
            }
            closeModal();
        }

        function manualScanMode() {
            closeModal();
            showModal('وضع المسح اليدوي', `
                <div style="text-align: center;">
                    <i class="fas fa-hand-paper" style="font-size: 48px; color: #3498db; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">ارفع صور المستندات</h3>
                    <div style="border: 3px dashed #bdc3c7; border-radius: 15px; padding: 40px; margin-bottom: 20px; background: #f8f9fa;">
                        <input type="file" id="manualScanFiles" multiple accept="image/*" style="display: none;" onchange="handleManualScan(this.files)">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 36px; color: #bdc3c7; margin-bottom: 15px;"></i>
                        <p style="color: #7f8c8d; margin-bottom: 15px;">انقر لاختيار الصور أو اسحبها هنا</p>
                        <button onclick="document.getElementById('manualScanFiles').click()" style="background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                            اختيار الصور
                        </button>
                    </div>
                </div>
            `);
        }

        function handleManualScan(files) {
            if (files.length > 0) {
                const subject = document.getElementById('bookSubject').value || 'مسح_يدوي';
                showProgressBar(`جاري معالجة ${files.length} صورة...`, 2000);
                
                setTimeout(() => {
                    closeModal();
                    showAlert(`تم رفع ${files.length} صورة بنجاح`, 'success');
                    showAlert(`تم حفظ الصور باسم: ${subject}_001, ${subject}_002...`, 'info');
                }, 2000);
            }
        }

        function performScan() {
            closeModal();
            showProgressBar('جاري المسح عبر نظام Windows...', 4000);
            
            setTimeout(() => {
                const subject = document.getElementById('bookSubject').value || 'مسح_نظام';
                const imageCount = Math.floor(Math.random() * 3) + 1;
                
                for (let i = 1; i <= imageCount; i++) {
                    const imageName = `${subject}_${String(i).padStart(3, '0')}.jpg`;
                    setTimeout(() => {
                        showAlert(`تم مسح الصفحة ${i}: ${imageName}`, 'success');
                    }, i * 1000);
                }
                
                setTimeout(() => {
                    showAlert(`تم إنجاز المسح بنجاح - ${imageCount} صورة`, 'success');
                }, (imageCount + 1) * 1000);
            }, 1000);
        }

        function stopScanner() {
            closeModal();
            showAlert('تم إيقاف الماسح الضوئي', 'info');
        }

        function handleBookImages(files) {
            if (files.length > 0) {
                showProgressBar(`جاري معالجة ${files.length} صورة...`, 2000);
                setTimeout(() => {
                    const subject = document.getElementById('bookSubject').value || 'كتاب_جديد';
                    showAlert(`تم حفظ ${files.length} صورة باسم "${subject}" في D:/ALI-AJIL-Arshef/صور الارشيف/`, 'success');
                }, 2000);
            }
        }

        function saveBookData() {
            const bookNumber = document.getElementById('bookNumber').value;
            const bookDay = document.getElementById('bookDay').value;
            const bookMonth = document.getElementById('bookMonth').value;
            const bookYear = document.getElementById('bookYear').value;
            const bookSubject = document.getElementById('bookSubject').value;
            const bookDetails = document.getElementById('bookDetails').value;
            const bookExecution = document.getElementById('bookExecution').value;

            if (!bookNumber || !bookSubject) {
                showAlert('يرجى إدخال رقم الكتاب والموضوع', 'warning');
                return;
            }

            showProgressBar('جاري حفظ بيانات الكتاب في D:/ALI-AJIL-Arshef/بيانات الارشفة...', 3000);
            
            setTimeout(() => {
                // محاكاة حفظ البيانات
                const bookData = {
                    number: bookNumber,
                    date: `${bookDay}/${bookMonth}/${bookYear}`,
                    subject: bookSubject,
                    details: bookDetails,
                    execution: bookExecution,
                    timestamp: new Date().toISOString()
                };
                
                // حفظ في localStorage كمحاكاة
                let savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
                savedBooks.push(bookData);
                localStorage.setItem('archivedBooks', JSON.stringify(savedBooks));
                
                showAlert('تم حفظ بيانات الكتاب بنجاح في D:/ALI-AJIL-Arshef/', 'success');
                updateDashboardStats();
                clearBookForm();
            }, 3000);
        }

        function clearBookForm() {
            document.getElementById('bookNumber').value = '';
            document.getElementById('bookDay').value = '';
            document.getElementById('bookMonth').value = '';
            document.getElementById('bookYear').value = '';
            document.getElementById('bookSubject').value = '';
            document.getElementById('bookDetails').value = '';
            document.getElementById('bookExecution').value = '';
            showAlert('تم مسح جميع الحقول', 'info');
        }

        function showProgrammerInfo() {
            showModal('معلومات المبرمج', `
                <div style="text-align: center;">
                    <div style="width: 100px; height: 100px; background: linear-gradient(45deg, #e74c3c, #3498db, #f39c12, #9b59b6); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 36px; font-weight: 700; animation: gradientShift 3s ease-in-out infinite;">
                        ع.ع
                    </div>
                    <h2 style="color: #2c3e50; margin-bottom: 10px; background: linear-gradient(45deg, #e74c3c, #3498db, #f39c12, #9b59b6); background-size: 400% 400%; -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: gradientShift 3s ease-in-out infinite;">
                        علي عاجل خشام المحنة
                    </h2>
                    <p style="color: #7f8c8d; margin-bottom: 20px; font-size: 16px;">مبرمج ومطور نظام إدارة المكتبة الاحترافي</p>
                    <div style="background: rgba(52, 152, 219, 0.1); padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">ميزات النظام:</h4>
                        <ul style="text-align: right; color: #7f8c8d; line-height: 2;">
                            <li>نظام أرشفة متقدم مع ماسح ضوئي</li>
                            <li>حفظ تلقائي في D:/ALI-AJIL-Arshef/</li>
                            <li>واجهة مستخدم تفاعلية ومتجاوبة</li>
                            <li>نظام بحث متقدم وتقارير شاملة</li>
                        </ul>
                    </div>
                    <p style="color: #27ae60; font-weight: 600;">تم تطوير هذا النظام بعناية وإبداع لخدمة المكتبات العربية</p>
                </div>
            `);
        }

        // تفريغ البيانات الوهمية
        function clearDummyData() {
            localStorage.removeItem('dummyBooks');
            localStorage.removeItem('dummyUsers');
            localStorage.removeItem('dummyTransactions');
            console.log('تم تفريغ جميع البيانات الوهمية من النظام');
        }

        // وظائف إضافية للبحث
        function exportSearchResults() {
            showAlert('جاري تصدير نتائج البحث...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير النتائج بنجاح', 'success');
            }, 1500);
        }

        function viewBookDetails(bookNumber) {
            const savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
            const book = savedBooks.find(b => b.number === bookNumber);
            if (book) {
                showModal('تفاصيل الكتاب وعرض الصور', `
                    <div style="display: grid; grid-template-columns: 300px 1fr; gap: 30px;">
                        <div style="text-align: center;">
                            <h3 style="color: #2c3e50; margin-bottom: 15px;">صور الكتاب</h3>
                            <div style="border: 2px solid #ecf0f1; border-radius: 10px; padding: 20px; background: #f8f9fa;">
                                <div id="bookImages" style="display: grid; gap: 10px;">
                                    <div style="width: 200px; height: 250px; background: linear-gradient(45deg, #3498db, #2980b9); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 48px; margin: 0 auto;">
                                        <i class="fas fa-image"></i>
                                    </div>
                                    <p style="color: #7f8c8d; font-size: 14px; margin-top: 10px;">${book.subject}_001.jpg</p>
                                    <button onclick="viewAllImages('${book.number}')" style="background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; margin-top: 10px;">
                                        <i class="fas fa-images" style="margin-left: 5px;"></i>
                                        عرض جميع الصور
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div style="text-align: right; line-height: 2;">
                            <h3 style="color: #2c3e50; margin-bottom: 20px;">بيانات الكتاب</h3>
                            <p><strong>رقم الكتاب:</strong> ${book.number}</p>
                            <p><strong>التاريخ:</strong> ${book.date}</p>
                            <p><strong>الموضوع:</strong> ${book.subject}</p>
                            <p><strong>التفاصيل:</strong> ${book.details}</p>
                            <p><strong>التنفيذ:</strong> ${book.execution}</p>
                            <p><strong>تاريخ الحفظ:</strong> ${new Date(book.timestamp).toLocaleString('ar-SA')}</p>
                        </div>
                    </div>
                `, 'large');
            }
        }

        function viewAllImages(bookNumber) {
            showModal('جميع صور الكتاب', `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; max-height: 500px; overflow-y: auto;">
                    <div style="text-align: center; border: 2px solid #ecf0f1; border-radius: 10px; padding: 15px;">
                        <div style="width: 180px; height: 220px; background: linear-gradient(45deg, #3498db, #2980b9); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 36px; margin: 0 auto 10px;">
                            <i class="fas fa-image"></i>
                        </div>
                        <p style="color: #2c3e50; font-weight: 600;">الصفحة 1</p>
                    </div>
                    <div style="text-align: center; border: 2px solid #ecf0f1; border-radius: 10px; padding: 15px;">
                        <div style="width: 180px; height: 220px; background: linear-gradient(45deg, #27ae60, #229954); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 36px; margin: 0 auto 10px;">
                            <i class="fas fa-image"></i>
                        </div>
                        <p style="color: #2c3e50; font-weight: 600;">الصفحة 2</p>
                    </div>
                </div>
            `, 'large');
        }

        function editBookData(bookNumber) {
            const savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
            const book = savedBooks.find(b => b.number === bookNumber);
            if (book) {
                showModal('تعديل بيانات الكتاب', `
                    <form onsubmit="updateBookData(event, '${bookNumber}')" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">رقم الكتاب</label>
                            <input type="text" id="editBookNumber" value="${book.number}" style="width: 100%; padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                        </div>
                        <div>
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">التاريخ</label>
                            <input type="text" id="editBookDate" value="${book.date}" style="width: 100%; padding: 10px 15px; border: 2px solid #ecf0f1; border-radius: 8px; outline: none;">
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">الموضوع</label>
                            <textarea id="editBookSubject" rows="3" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; resize: vertical;">${book.subject}</textarea>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">التفاصيل</label>
                            <textarea id="editBookDetails" rows="4" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; resize: vertical;">${book.details}</textarea>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">التنفيذ</label>
                            <textarea id="editBookExecution" rows="3" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none; resize: vertical;">${book.execution}</textarea>
                        </div>
                        <div style="grid-column: 1 / -1; display: flex; gap: 15px; justify-content: center; margin-top: 20px;">
                            <button type="submit" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                                <i class="fas fa-save" style="margin-left: 8px;"></i>
                                حفظ التعديلات
                            </button>
                            <button type="button" onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                                <i class="fas fa-times" style="margin-left: 8px;"></i>
                                إلغاء
                            </button>
                        </div>
                    </form>
                `, 'large');
            }
        }

        function updateBookData(event, oldBookNumber) {
            event.preventDefault();
            
            const newNumber = document.getElementById('editBookNumber').value;
            const newDate = document.getElementById('editBookDate').value;
            const newSubject = document.getElementById('editBookSubject').value;
            const newDetails = document.getElementById('editBookDetails').value;
            const newExecution = document.getElementById('editBookExecution').value;
            
            let savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
            const bookIndex = savedBooks.findIndex(b => b.number === oldBookNumber);
            
            if (bookIndex !== -1) {
                savedBooks[bookIndex] = {
                    ...savedBooks[bookIndex],
                    number: newNumber,
                    date: newDate,
                    subject: newSubject,
                    details: newDetails,
                    execution: newExecution,
                    lastModified: new Date().toISOString()
                };
                
                localStorage.setItem('archivedBooks', JSON.stringify(savedBooks));
                closeModal();
                performAdvancedSearch();
                updateDashboardStats();
                showAlert('تم تحديث بيانات الكتاب بنجاح', 'success');
            }
        }

        function deleteBookData(bookNumber) {
            showModal('تأكيد الحذف', `
                <div style="text-align: center;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #e74c3c; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">هل أنت متأكد من حذف هذا الكتاب؟</h3>
                    <p style="color: #7f8c8d; margin-bottom: 30px;">رقم الكتاب: ${bookNumber}</p>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="confirmDeleteBookData('${bookNumber}')" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-trash" style="margin-left: 8px;"></i>
                            تأكيد الحذف
                        </button>
                        <button onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            `);
        }

        function confirmDeleteBookData(bookNumber) {
            let savedBooks = JSON.parse(localStorage.getItem('archivedBooks') || '[]');
            const book = savedBooks.find(b => b.number === bookNumber);
            
            if (book) {
                // حذف الصور المرتبطة
                const imagesToDelete = [`${book.subject}_001.jpg`, `${book.subject}_002.jpg`, `${book.subject}_003.jpg`];
                showAlert(`جاري حذف الصور: ${imagesToDelete.join(', ')}`, 'info');
                
                // حذف بيانات الكتاب
                savedBooks = savedBooks.filter(book => book.number !== bookNumber);
                localStorage.setItem('archivedBooks', JSON.stringify(savedBooks));
                
                closeModal();
                performAdvancedSearch();
                updateDashboardStats();
                showAlert('تم حذف الكتاب وصوره بنجاح', 'success');
            }
        }

        // تشغيل تفريغ البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            clearDummyData();
            updateDashboardStats();
            if (currentSection === 'backup') {
                updateBackupTable();
            }
        });

        // تحديث جدول النسخ عند عرض القسم
        const originalShowSection = showSection;
        showSection = function(sectionName) {
            originalShowSection.call(this, sectionName);
            if (sectionName === 'backup') {
                setTimeout(() => {
                    updateBackupTable();
                }, 100);
            }
        }

        // وظائف النسخ الاحتياطي
        function createBackup() {
            showModal('إنشاء نسخة احتياطية', `
                <div style="text-align: center;">
                    <i class="fas fa-save" style="font-size: 48px; color: #16a085; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">اختر موقع حفظ النسخة الاحتياطية</h3>
                    <input type="file" id="backupLocation" webkitdirectory style="display: none;">
                    <button onclick="selectBackupLocation()" style="background: linear-gradient(45deg, #16a085, #1abc9c); color: white; border: none; padding: 15px 30px; border-radius: 10px; cursor: pointer; font-weight: 600; margin-bottom: 20px;">
                        <i class="fas fa-folder-open" style="margin-left: 8px;"></i>
                        اختيار مجلد الحفظ
                    </button>
                    <div id="selectedPath" style="color: #7f8c8d; margin-bottom: 20px; font-size: 14px;"></div>
                    <button onclick="performBackup()" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-play" style="margin-left: 8px;"></i>
                        بدء إنشاء النسخة
                    </button>
                </div>
            `);
        }

        function selectBackupLocation() {
            document.getElementById('backupLocation').click();
            document.getElementById('selectedPath').textContent = 'تم اختيار مجلد الحفظ';
        }

        function performBackup() {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupName = `ALI-AJIL-Backup_${timestamp}`;
            
            showProgressBar('جاري إنشاء النسخة الاحتياطية...', 4000);
            
            setTimeout(() => {
                const backupData = {
                    books: JSON.parse(localStorage.getItem('archivedBooks') || '[]'),
                    users: JSON.parse(localStorage.getItem('systemUsers') || '[]'),
                    transactions: JSON.parse(localStorage.getItem('transactions') || '[]'),
                    settings: JSON.parse(localStorage.getItem('systemSettings') || '{}'),
                    timestamp: new Date().toISOString(),
                    version: '2.0'
                };
                
                // حفظ في localStorage للعرض
                let backups = JSON.parse(localStorage.getItem('systemBackups') || '[]');
                backups.push({
                    name: backupName,
                    date: new Date().toLocaleString('ar-SA'),
                    size: JSON.stringify(backupData).length + ' bytes',
                    type: 'كاملة',
                    status: 'سليمة'
                });
                localStorage.setItem('systemBackups', JSON.stringify(backups));
                
                closeModal();
                updateBackupTable();
                showAlert(`تم إنشاء النسخة الاحتياطية: ${backupName}`, 'success');
            }, 4000);
        }

        function restoreBackup() {
            showModal('استعادة نسخة احتياطية', `
                <div style="text-align: center;">
                    <i class="fas fa-undo" style="font-size: 48px; color: #3498db; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">اختر ملف النسخة الاحتياطية</h3>
                    <input type="file" id="restoreFile" accept=".json,.zip" style="display: none;" onchange="handleRestoreFile(this.files[0])">
                    <button onclick="document.getElementById('restoreFile').click()" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 15px 30px; border-radius: 10px; cursor: pointer; font-weight: 600; margin-bottom: 20px;">
                        <i class="fas fa-file-import" style="margin-left: 8px;"></i>
                        اختيار ملف النسخة
                    </button>
                    <div id="restoreFileName" style="color: #7f8c8d; margin-bottom: 20px; font-size: 14px;"></div>
                    <button id="performRestoreBtn" onclick="performRestore()" disabled style="background: #95a5a6; color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: not-allowed; font-weight: 600;">
                        <i class="fas fa-play" style="margin-left: 8px;"></i>
                        بدء الاستعادة
                    </button>
                </div>
            `);
        }

        function handleRestoreFile(file) {
            if (file) {
                document.getElementById('restoreFileName').textContent = `تم اختيار: ${file.name}`;
                const btn = document.getElementById('performRestoreBtn');
                btn.disabled = false;
                btn.style.background = 'linear-gradient(45deg, #27ae60, #229954)';
                btn.style.cursor = 'pointer';
            }
        }

        function performRestore() {
            showProgressBar('جاري استعادة النسخة الاحتياطية...', 3000);
            
            setTimeout(() => {
                closeModal();
                showAlert('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                updateDashboardStats();
            }, 3000);
        }

        function updateBackupTable() {
            const backups = JSON.parse(localStorage.getItem('systemBackups') || '[]');
            const tbody = document.getElementById('backupTableBody');
            
            if (backups.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td style="padding: 15px; color: #7f8c8d; text-align: center;" colspan="6">
                            <i class="fas fa-database" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px; display: block;"></i>
                            <p style="color: #95a5a6; font-size: 16px;">لا توجد نسخ احتياطية محفوظة</p>
                            <p style="color: #7f8c8d; font-size: 14px;">قم بإنشاء نسخة احتياطية لحماية بياناتك</p>
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = backups.map(backup => `
                <tr style="border-bottom: 1px solid rgba(0,0,0,0.1);">
                    <td style="padding: 15px; color: #2c3e50; font-weight: 600;">${backup.name}</td>
                    <td style="padding: 15px; color: #7f8c8d;">${backup.date}</td>
                    <td style="padding: 15px; color: #7f8c8d;">${backup.size}</td>
                    <td style="padding: 15px;"><span style="background: #3498db; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">${backup.type}</span></td>
                    <td style="padding: 15px;"><span style="background: #27ae60; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">${backup.status}</span></td>
                    <td style="padding: 15px;">
                        <button onclick="restoreSpecificBackup('${backup.name}')" style="background: #3498db; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 0 2px; cursor: pointer;">
                            <i class="fas fa-undo"></i>
                        </button>
                        <button onclick="downloadBackup('${backup.name}')" style="background: #27ae60; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 0 2px; cursor: pointer;">
                            <i class="fas fa-download"></i>
                        </button>
                        <button onclick="deleteBackup('${backup.name}')" style="background: #e74c3c; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 0 2px; cursor: pointer;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function restoreSpecificBackup(backupName) {
            showAlert(`جاري استعادة ${backupName}...`, 'info');
            setTimeout(() => {
                showAlert('تم استعادة النسخة بنجاح', 'success');
            }, 2000);
        }

        function downloadBackup(backupName) {
            showAlert(`جاري تحميل ${backupName}...`, 'info');
        }

        function deleteBackup(backupName) {
            let backups = JSON.parse(localStorage.getItem('systemBackups') || '[]');
            backups = backups.filter(b => b.name !== backupName);
            localStorage.setItem('systemBackups', JSON.stringify(backups));
            updateBackupTable();
            showAlert('تم حذف النسخة الاحتياطية', 'success');
        }

        // وظائف الماسح الضوئي Canon DR-C240 المتطورة

        // متغيرات الماسح الضوئي
        let scannerConnected = false;
        let scannedImages = [];
        let currentBookData = {};

        // وظيفة تشغيل الماسح الضوئي Canon DR-C240
        function startCanonScanner() {
            showProgressBar('جاري الاتصال بالماسح الضوئي...', 3000);

            // محاولة تشغيل برنامج Canon CaptureOnTouch
            const scannerPath = 'C:\\Program Files (x86)\\Canon Electronics\\CaptureOnTouch\\TouchDR.exe';

            try {
                // محاولة تشغيل الماسح الضوئي
                if (window.ActiveXObject || "ActiveXObject" in window) {
                    // للمتصفحات التي تدعم ActiveX (Internet Explorer)
                    const shell = new ActiveXObject("WScript.Shell");
                    shell.Run(scannerPath, 1, false);
                    scannerConnected = true;

                    setTimeout(() => {
                        showAlert('تم تشغيل الماسح الضوئي بنجاح!', 'success');
                        showScannerInterface();
                    }, 3000);
                } else {
                    // للمتصفحات الحديثة - استخدام File System Access API
                    launchScannerModern();
                }
            } catch (error) {
                console.error('خطأ في تشغيل الماسح الضوئي:', error);
                setTimeout(() => {
                    showAlert('تعذر الاتصال بالماسح الضوئي. تأكد من تثبيت برنامج Canon CaptureOnTouch', 'error');
                    showManualScanOption();
                }, 3000);
            }
        }

        // تشغيل الماسح الضوئي للمتصفحات الحديثة
        async function launchScannerModern() {
            try {
                // محاولة استخدام Web Serial API للاتصال بالماسح الضوئي
                if ('serial' in navigator) {
                    const port = await navigator.serial.requestPort();
                    await port.open({ baudRate: 9600 });
                    scannerConnected = true;
                    showAlert('تم الاتصال بالماسح الضوئي!', 'success');
                    showScannerInterface();
                } else {
                    // استخدام طريقة بديلة
                    showManualScanOption();
                }
            } catch (error) {
                console.error('خطأ في الاتصال:', error);
                showManualScanOption();
            }
        }

        // عرض خيار المسح اليدوي
        function showManualScanOption() {
            showModal('الماسح الضوئي Canon DR-C240', `
                <div style="text-align: center;">
                    <i class="fas fa-scanner" style="font-size: 64px; color: #3498db; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">تشغيل الماسح الضوئي</h3>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: right;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">خطوات التشغيل:</h4>
                        <ol style="color: #7f8c8d; line-height: 1.8;">
                            <li>تأكد من توصيل الماسح الضوئي Canon DR-C240 بالكمبيوتر</li>
                            <li>تشغيل برنامج Canon CaptureOnTouch من المسار:</li>
                            <li style="font-family: monospace; background: #e9ecef; padding: 5px; border-radius: 5px; margin: 10px 0;">
                                C:\\Program Files (x86)\\Canon Electronics\\CaptureOnTouch\\TouchDR.exe
                            </li>
                            <li>ضع المستندات في الماسح الضوئي</li>
                            <li>اضغط على زر "بدء المسح" أدناه</li>
                        </ol>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                        <button onclick="startManualScan()" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-play" style="margin-left: 8px;"></i>
                            بدء المسح
                        </button>
                        <button onclick="openScannerFolder()" style="background: linear-gradient(45deg, #f39c12, #e67e22); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-folder-open" style="margin-left: 8px;"></i>
                            فتح مجلد الصور
                        </button>
                        <button onclick="uploadScannedImages()" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 12px 25px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-upload" style="margin-left: 8px;"></i>
                            رفع الصور المسحوبة
                        </button>
                    </div>

                    <div style="margin-top: 20px; padding: 15px; background: rgba(52, 152, 219, 0.1); border-radius: 10px;">
                        <p style="color: #2c3e50; font-weight: 600; margin-bottom: 10px;">
                            <i class="fas fa-info-circle" style="margin-left: 8px;"></i>
                            معلومات الماسح الضوئي
                        </p>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; text-align: right;">
                            <div><strong>الطراز:</strong> Canon DR-C240</div>
                            <div><strong>السرعة:</strong> 45 صفحة/دقيقة</div>
                            <div><strong>الدقة:</strong> 600 DPI</div>
                            <div><strong>الألوان:</strong> ملون/رمادي/أبيض وأسود</div>
                        </div>
                    </div>
                </div>
            `, 'large');
        }

        // بدء المسح اليدوي
        function startManualScan() {
            closeModal();
            showProgressBar('جاري المسح الضوئي...', 5000);

            // محاكاة عملية المسح
            setTimeout(() => {
                showAlert('تم المسح بنجاح! يمكنك الآن رفع الصور', 'success');
                showScanResults();
            }, 5000);
        }

        // فتح مجلد الصور
        function openScannerFolder() {
            try {
                // محاولة فتح مجلد الصور الافتراضي للماسح الضوئي
                const shell = new ActiveXObject("WScript.Shell");
                shell.Run("explorer.exe C:\\Users\\<USER>\\Documents\\Canon\\CaptureOnTouch", 1, false);
                showAlert('تم فتح مجلد الصور', 'info');
            } catch (error) {
                showAlert('يرجى فتح مجلد الصور يدوياً من: C:\\Users\\<USER>\\Documents\\Canon\\CaptureOnTouch', 'info');
            }
        }

        // رفع الصور المسحوبة
        function uploadScannedImages() {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = 'image/*,.pdf';

            input.onchange = function(event) {
                const files = event.target.files;
                if (files.length > 0) {
                    handleScannedFiles(files);
                }
            };

            input.click();
        }

        // معالجة الملفات المسحوبة
        function handleScannedFiles(files) {
            showProgressBar('جاري معالجة الصور المسحوبة...', 3000);

            scannedImages = [];

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const reader = new FileReader();

                reader.onload = function(e) {
                    scannedImages.push({
                        name: file.name,
                        data: e.target.result,
                        size: file.size,
                        type: file.type,
                        lastModified: file.lastModified
                    });

                    // إذا تم تحميل جميع الصور
                    if (scannedImages.length === files.length) {
                        setTimeout(() => {
                            showAlert(`تم رفع ${files.length} صورة بنجاح!`, 'success');
                            displayScannedImages();
                        }, 3000);
                    }
                };

                reader.readAsDataURL(file);
            }
        }

        // عرض الصور المسحوبة
        function displayScannedImages() {
            closeModal();

            let imagesHtml = '';
            scannedImages.forEach((image, index) => {
                imagesHtml += `
                    <div style="border: 2px solid #ecf0f1; border-radius: 10px; padding: 15px; margin-bottom: 15px; display: flex; align-items: center; gap: 15px;">
                        <img src="${image.data}" style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px;">
                        <div style="flex: 1;">
                            <h4 style="color: #2c3e50; margin-bottom: 5px;">${image.name}</h4>
                            <p style="color: #7f8c8d; font-size: 14px;">الحجم: ${(image.size / 1024).toFixed(2)} KB</p>
                            <p style="color: #7f8c8d; font-size: 14px;">النوع: ${image.type}</p>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button onclick="editImage(${index})" style="background: #3498db; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteImage(${index})" style="background: #e74c3c; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            });

            showModal('الصور المسحوبة', `
                <div style="max-height: 500px; overflow-y: auto;">
                    ${imagesHtml}
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="attachImagesToBook()" style="background: linear-gradient(45deg, #27ae60, #229954); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-paperclip" style="margin-left: 8px;"></i>
                        ربط الصور بالكتاب
                    </button>
                    <button onclick="saveImagesToLibrary()" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-save" style="margin-left: 8px;"></i>
                        حفظ في المكتبة
                    </button>
                </div>
            `, 'large');
        }

        // ربط الصور بالكتاب
        function attachImagesToBook() {
            if (scannedImages.length > 0) {
                currentBookData.images = scannedImages;
                closeModal();
                showAlert('تم ربط الصور بالكتاب بنجاح!', 'success');

                // تحديث واجهة إضافة الكتاب لإظهار الصور المرفقة
                updateBookFormWithImages();
            } else {
                showAlert('لا توجد صور لربطها', 'warning');
            }
        }

        // تحديث نموذج الكتاب مع الصور
        function updateBookFormWithImages() {
            const imageContainer = document.querySelector('#bookImages').parentElement;
            if (imageContainer && scannedImages.length > 0) {
                imageContainer.innerHTML = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); gap: 10px;">
                        ${scannedImages.map((img, index) => `
                            <div style="position: relative;">
                                <img src="${img.data}" style="width: 100%; height: 80px; object-fit: cover; border-radius: 5px;">
                                <button onclick="removeImageFromBook(${index})" style="position: absolute; top: -5px; right: -5px; background: #e74c3c; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; font-size: 12px; cursor: pointer;">×</button>
                            </div>
                        `).join('')}
                    </div>
                    <p style="color: #27ae60; font-weight: 600; margin-top: 10px;">
                        <i class="fas fa-check-circle" style="margin-left: 5px;"></i>
                        تم ربط ${scannedImages.length} صورة
                    </p>
                `;
            }
        }

        // حذف صورة من الكتاب
        function removeImageFromBook(index) {
            scannedImages.splice(index, 1);
            updateBookFormWithImages();
            showAlert('تم حذف الصورة', 'info');
        }

        // حفظ بيانات الكتاب مع الصور
        function saveBookData() {
            const bookData = {
                number: document.getElementById('bookNumber').value,
                day: document.getElementById('bookDay').value,
                month: document.getElementById('bookMonth').value,
                year: document.getElementById('bookYear').value,
                subject: document.getElementById('bookSubject').value,
                details: document.getElementById('bookDetails').value,
                execution: document.getElementById('bookExecution').value,
                images: scannedImages,
                timestamp: new Date().toISOString()
            };

            if (!bookData.number || !bookData.subject) {
                showAlert('يرجى ملء الحقول المطلوبة (رقم الكتاب والموضوع)', 'warning');
                return;
            }

            // حفظ البيانات في localStorage
            let savedBooks = JSON.parse(localStorage.getItem('libraryBooks') || '[]');
            savedBooks.push(bookData);
            localStorage.setItem('libraryBooks', JSON.stringify(savedBooks));

            showAlert('تم حفظ بيانات الكتاب بنجاح!', 'success');
            clearBookForm();
            updateDashboardStats();
        }

        // مسح نموذج الكتاب
        function clearBookForm() {
            document.getElementById('bookNumber').value = '';
            document.getElementById('bookDay').value = '';
            document.getElementById('bookMonth').value = '';
            document.getElementById('bookYear').value = '';
            document.getElementById('bookSubject').value = '';
            document.getElementById('bookDetails').value = '';
            document.getElementById('bookExecution').value = '';
            scannedImages = [];

            // إعادة تعيين منطقة الصور
            const imageContainer = document.querySelector('#bookImages').parentElement;
            if (imageContainer) {
                imageContainer.innerHTML = `
                    <i class="fas fa-camera" style="font-size: 32px; color: #7f8c8d; margin-bottom: 10px;"></i>
                    <button onclick="startCanonScanner()" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; margin-bottom: 10px;">
                        <i class="fas fa-scanner" style="margin-left: 8px;"></i>
                        تشغيل الماسح الضوئي DR-C240
                    </button>
                    <small style="color: #7f8c8d;">أو اسحب الصور هنا</small>
                    <input type="file" id="bookImages" multiple accept="image/*" style="display: none;" onchange="handleBookImages(this.files)">
                `;
            }
        }

        // معالجة صور الكتاب
        function handleBookImages(files) {
            handleScannedFiles(files);
        }

        // بدء عملية النسخ الاحتياطي
        function startBackupProcess() {
            const backupName = document.getElementById('backupName').value;
            const backupPath = document.getElementById('backupPath').value;

            if (!backupName.trim()) {
                showAlert('يرجى إدخال اسم للنسخة الاحتياطية', 'warning');
                return;
            }

            closeModal();
            showProgressBar('جاري إنشاء النسخة الاحتياطية...', 5000);

            // جمع جميع البيانات
            const allData = {
                archivedBooks: JSON.parse(localStorage.getItem('archivedBooks') || '[]'),
                libraryBooks: JSON.parse(localStorage.getItem('libraryBooks') || '[]'),
                systemSettings: JSON.parse(localStorage.getItem('systemSettings') || '{}'),
                systemBackups: JSON.parse(localStorage.getItem('systemBackups') || '[]'),
                timestamp: new Date().toISOString(),
                version: '1.0',
                creator: 'نظام إدارة المكتبة الاحترافي'
            };

            // حساب الإحصائيات
            const totalBooks = allData.archivedBooks.length + allData.libraryBooks.length;
            const totalImages = [...allData.archivedBooks, ...allData.libraryBooks]
                .reduce((total, book) => total + (book.images ? book.images.length : 0), 0);
            const dataSize = calculateDataSize([...allData.archivedBooks, ...allData.libraryBooks]);

            // إنشاء معرف فريد للنسخة
            const backupId = 'backup_' + Date.now();

            // إنشاء معلومات النسخة الاحتياطية
            const backupInfo = {
                id: backupId,
                name: backupName,
                path: backupPath,
                timestamp: allData.timestamp,
                size: dataSize.toFixed(2),
                booksCount: totalBooks,
                imagesCount: totalImages,
                data: allData
            };

            // حفظ النسخة الاحتياطية
            setTimeout(() => {
                try {
                    // إضافة النسخة إلى القائمة
                    backupList.push(backupInfo);
                    localStorage.setItem('systemBackups', JSON.stringify(backupList));

                    // محاولة حفظ الملف (في المتصفح سيتم تحميله)
                    downloadBackupAsFile(backupInfo);

                    updateBackupList();
                    showAlert(`تم إنشاء النسخة الاحتياطية "${backupName}" بنجاح!`, 'success');

                    // تحديث القسم إذا كان مفتوحاً
                    if (currentSection === 'backup') {
                        showSection('backup');
                    }
                } catch (error) {
                    console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
                    showAlert('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
                }
            }, 5000);
        }

        // تحميل النسخة الاحتياطية كملف
        function downloadBackupAsFile(backupInfo) {
            const dataStr = JSON.stringify(backupInfo.data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `${backupInfo.name}_${backupInfo.id}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // تنظيف الذاكرة
            URL.revokeObjectURL(link.href);
        }

        // استعادة نسخة احتياطية
        function restoreRealBackup() {
            showModal('استعادة نسخة احتياطية', `
                <div style="text-align: center;">
                    <i class="fas fa-undo" style="font-size: 64px; color: #3498db; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">استعادة النظام من نسخة احتياطية</h3>

                    <div style="background: rgba(231, 76, 60, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                        <p style="color: #e74c3c; font-weight: 600; margin: 0;">
                            <i class="fas fa-exclamation-triangle" style="margin-left: 8px;"></i>
                            تحذير: ستتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية
                        </p>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; color: #2c3e50; font-weight: 600; margin-bottom: 8px;">اختر ملف النسخة الاحتياطية</label>
                        <input type="file" id="restoreFile" accept=".json" style="width: 100%; padding: 12px 15px; border: 2px solid #ecf0f1; border-radius: 10px; outline: none;">
                        <small style="color: #7f8c8d;">اختر ملف النسخة الاحتياطية بصيغة JSON</small>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="startRestoreProcess()" style="background: linear-gradient(45deg, #3498db, #2980b9); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-upload" style="margin-left: 8px;"></i>
                            بدء الاستعادة
                        </button>
                        <button onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            `);
        }

        // بدء عملية الاستعادة
        function startRestoreProcess() {
            const fileInput = document.getElementById('restoreFile');
            const file = fileInput.files[0];

            if (!file) {
                showAlert('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
                return;
            }

            if (!file.name.endsWith('.json')) {
                showAlert('يرجى اختيار ملف بصيغة JSON', 'warning');
                return;
            }

            closeModal();
            showProgressBar('جاري استعادة النسخة الاحتياطية...', 4000);

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const backupData = JSON.parse(e.target.result);

                    // التحقق من صحة البيانات
                    if (!backupData.archivedBooks || !backupData.timestamp) {
                        throw new Error('ملف النسخة الاحتياطية غير صحيح');
                    }

                    setTimeout(() => {
                        // استعادة البيانات
                        localStorage.setItem('archivedBooks', JSON.stringify(backupData.archivedBooks || []));
                        localStorage.setItem('libraryBooks', JSON.stringify(backupData.libraryBooks || []));
                        localStorage.setItem('systemSettings', JSON.stringify(backupData.systemSettings || {}));

                        // تحديث النظام
                        updateBackupList();
                        updateDashboardStats();

                        showAlert('تم استعادة النسخة الاحتياطية بنجاح!', 'success');

                        // إعادة تحميل الصفحة لتطبيق التغييرات
                        setTimeout(() => {
                            if (confirm('هل تريد إعادة تحميل الصفحة لتطبيق التغييرات؟')) {
                                location.reload();
                            }
                        }, 2000);
                    }, 4000);

                } catch (error) {
                    console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                    showAlert('خطأ في قراءة ملف النسخة الاحتياطية', 'error');
                }
            };

            reader.readAsText(file);
        }

        // تحديث قائمة النسخ الاحتياطية
        function refreshBackupList() {
            updateBackupList();
            showAlert('تم تحديث قائمة النسخ الاحتياطية', 'info');
        }

        // تنظيف النسخ القديمة
        function cleanOldBackups() {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const oldBackups = backupList.filter(backup => new Date(backup.timestamp) < thirtyDaysAgo);

            if (oldBackups.length === 0) {
                showAlert('لا توجد نسخ قديمة للحذف', 'info');
                return;
            }

            showModal('تنظيف النسخ القديمة', `
                <div style="text-align: center;">
                    <i class="fas fa-broom" style="font-size: 48px; color: #f39c12; margin-bottom: 20px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">تنظيف النسخ الاحتياطية القديمة</h3>
                    <p style="color: #7f8c8d; margin-bottom: 20px;">تم العثور على ${oldBackups.length} نسخة احتياطية أقدم من 30 يوماً</p>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="confirmCleanOldBackups()" style="background: linear-gradient(45deg, #f39c12, #e67e22); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-check" style="margin-left: 8px;"></i>
                            تأكيد الحذف
                        </button>
                        <button onclick="closeModal()" style="background: linear-gradient(45deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 30px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-times" style="margin-left: 8px;"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            `);
        }

        // تأكيد تنظيف النسخ القديمة
        function confirmCleanOldBackups() {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const oldCount = backupList.filter(backup => new Date(backup.timestamp) < thirtyDaysAgo).length;
            backupList = backupList.filter(backup => new Date(backup.timestamp) >= thirtyDaysAgo);

            localStorage.setItem('systemBackups', JSON.stringify(backupList));
            updateBackupList();
            closeModal();
            showAlert(`تم حذف ${oldCount} نسخة احتياطية قديمة`, 'success');
        }

        console.log('تم تحميل نظام إدارة المكتبة الاحترافي بنجاح! 🚀');
        console.log('ميزات جديدة: ماسح DR-C240، بحث متقدم، نسخ احتياطي حقيقي');
        console.log('مسار الحفظ: D:/ALI-AJIL-Arshef/');
        console.log('الماسح الضوئي: Canon DR-C240 مع معالجة متقدمة للصور');
        console.log('النسخ الاحتياطي: نظام متطور مع حفظ واستعادة حقيقية');
    </script>
</body>
</html>
